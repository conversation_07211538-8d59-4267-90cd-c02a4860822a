package exchange.admin.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import exchange.admin.constant.AdminAuthority;
import exchange.admin.entity.AdminUser;
import exchange.admin.entity.AdminUserAuthority;
import exchange.common.constant.CandlestickType;
import exchange.common.constant.Currency;
import exchange.common.constant.DepositStatus;
import exchange.common.constant.TmsStatus;
import exchange.common.entity.Deposit;
import exchange.common.entity.ExcessiveDepositUserByOneTime;
import exchange.common.service.ExcessiveDepositUserByOneTimeService;
import exchange.spot.entity.SpotCandlestickAdaJpy;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@AutoConfigureMockMvc(addFilters = false)
@ActiveProfiles({"test"})
class V1ReportExcessiveDepositByOneTimeControllerTest extends BaseReaderTest {

  @Autowired
  V1ReportExcessiveDepositUserByOneTimeController controller;

  @Autowired
  ExcessiveDepositUserByOneTimeService service;

  @BeforeEach
  public void beforeEach() {
    // テーブルをリセットする
    executeSql(masterEM, "DELETE FROM excessive_deposit_user_by_one_time");
    executeSql(masterEM, "DELETE FROM deposit");
    executeSql(masterEM, "DELETE FROM spot_candlestick_ada_jpy");
  }

  protected static Deposit createDeposit(Long userId, Currency currency, BigDecimal amount, BigDecimal fee, Date date) {
    var deposit = new Deposit();
    deposit.setUserId(userId);
    deposit.setCurrency(currency);
    deposit.setAmount(amount);
    deposit.setFee(fee);
    deposit.setAddress("-");
    deposit.setDepositAccountId(1L);
    deposit.setDepositStatus(DepositStatus.DONE);
    deposit.setCreatedAt(date);
    deposit.setUpdatedAt(date);
    return deposit;
  }

  @DisplayName("GET")
  @Test
  void getTest() throws Exception {

    // データを作成する
    {
      var deposit = createDeposit(1L, Currency.ADA, new BigDecimal("1"), new BigDecimal("2"), toDate(********));
      masterEM.getTransaction().begin();
      masterEM.persist(deposit);
      masterEM.getTransaction().commit();
    }
    {
      var deposit2 = createDeposit(2L, Currency.ADA, new BigDecimal("3"), new BigDecimal("4"), toDate(********));
      masterEM.getTransaction().begin();
      masterEM.persist(deposit2);
      masterEM.getTransaction().commit();
    }
    var candleStick = new SpotCandlestickAdaJpy();
    candleStick.setSymbolId(4L);
    candleStick.setCandlestickType(CandlestickType.PT1M);
    candleStick.setClose(new BigDecimal("*********.0"));
    candleStick.setTargetAt(toDate(********));
    candleStick.setCreatedAt(toDate(********));
    candleStick.setUpdatedAt(toDate(********));

    masterEM.getTransaction().begin();
    masterEM.persist(candleStick);
    masterEM.getTransaction().commit();

    // excessive_deposit_user_by_one_timeの検知を実行する
    var date = LocalDate.of(2022, 2, 2);
    service.execute(Currency.ADA, date);

    var adminUser = new AdminUser();
    var adminUserAuth = new AdminUserAuthority(1L, AdminAuthority.ADMINISTRATOR);
    adminUser.setAuthorities(List.of(adminUserAuth));

    MockHttpServletResponse response = new MockHttpServletResponse();
    var res = controller.get(response, adminUser, null, null, null, null, 0, 1000);
    assertEquals(res.getBody().getTotalElements(), 1);
  }

  @DisplayName("apply")
  @Test
  void applyTest() throws Exception {

    // create test data
    {
      var data = new ExcessiveDepositUserByOneTime();
      data.setUserId(1L);
      data.setEmail("test");
      data.setAmount(new BigDecimal("1"));
      data.setIncome(1);
      data.setFinancialAssets(1);
      data.setTargetAt(toDate(********));
      data.setTmsStatus(TmsStatus.OPEN);
      data.setCreatedAt(toDate(********));
      data.setUpdatedAt(toDate(********));

      masterEM.getTransaction().begin();
      masterEM.persist(data);
      masterEM.getTransaction().commit();
    }

    var data = service.findAll().get(0);


    var adminUser = new AdminUser();
    var adminUserAuth = new AdminUserAuthority(1L, AdminAuthority.ADMINISTRATOR);
    adminUser.setAuthorities(List.of(adminUserAuth));

    controller.put(adminUser, data.getId(), TmsStatus.CLOSE);

    var updated = service.findAll().get(0);
    assertEquals(updated.getTmsStatus(), TmsStatus.CLOSE);
  }
}