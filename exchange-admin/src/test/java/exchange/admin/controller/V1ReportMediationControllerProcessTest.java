package exchange.admin.controller;

import static org.assertj.core.api.Assertions.assertThat;

import exchange.admin.controller.V1ReportMediationController.ReadData;
import exchange.admin.model.response.ReportMediationStatement;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.OrderSide;
import java.util.ArrayList;
import java.util.List;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@AutoConfigureMockMvc(addFilters = false)
@ActiveProfiles({"test"})
class V1ReportMediationControllerProcessTest extends BaseProcessorTest {

  @Autowired
  V1ReportMediationController controller;

  @DisplayName("データなしの場合")
  @Test
  void emptyTest() {
    // **************** 初期値を設定する ****************
    final var symbols = createSymbols();
    final var readData = new ReadData(
        symbols,
        new ArrayList<>(),
        new ArrayList<>()
    );

    // **************** 処理を実行する ****************
    final var writeDtos = controller.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データなし").isEmpty();
  }

  @DisplayName("約定のみの場合")
  @Test
  void tradeTest() {
    // **************** 初期値を設定する ****************
    final var symbols = createSymbols();
    final var users = List.of(
        createPersonalUser(1L, false, "lastName", "firstName"),
        createCorporateUser(2L, true, "corporateName")
    );
    final var trades = List.of(
        createTrade(
            1L, // 約定ID
            11L, // 注文ID
            1L, // ユーザID
            CurrencyPair.ADA_JPY, // 通貨ペア
            OrderSide.BUY, // 売買
            big("1"), // 価格
            big("2"), // 数量
            big("3"), // 約定代金
            big("4"), // 約定手数料
            toDate("2022/02/02 02:02:01") // 作成日時(約定日時)
        ),
        createTrade(
            2L, // 約定ID
            12L, // 注文ID
            1L, // ユーザID
            CurrencyPair.ADA_JPY, // 通貨ペア
            OrderSide.SELL, // 売買
            big("5"), // 価格
            big("6"), // 数量
            big("7"), // 約定代金
            big("8"), // 約定手数料
            toDate("2022/02/02 02:02:02") // 作成日時(約定日時)
        ),
        createTrade(
            3L, // 約定ID
            13L, // 注文ID
            2L, // ユーザID
            CurrencyPair.ADA_JPY, // 通貨ペア
            OrderSide.BUY, // 売買
            big("11"), // 価格
            big("12"), // 数量
            big("13"), // 約定代金
            big("14"), // 約定手数料
            toDate("2022/02/02 02:02:11") // 作成日時(約定日時)
        ),
        createTrade(
            4L, // 約定ID
            14L, // 注文ID
            2L, // ユーザID
            CurrencyPair.ADA_JPY, // 通貨ペア
            OrderSide.SELL, // 売買
            big("15"), // 価格
            big("16"), // 数量
            big("17"), // 約定代金
            big("18"), // 約定手数料
            toDate("2022/02/02 02:02:12") // 作成日時(約定日時)
        )
    );
    final var readData = new ReadData(
        symbols,
        users,
        trades
    );

    // **************** 処理を実行する ****************
    final var writeDtos = controller.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos)
        .as("id, 注文ID, 約定ID, 顧客ID, 利用者の氏名又は名称, 媒介又は代理を行った年月日, date")
        .extracting(
            ReportMediationStatement::tradeIdLong,
            ReportMediationStatement::orderId,
            ReportMediationStatement::tradeId,
            ReportMediationStatement::userId,
            ReportMediationStatement::userName,
            ReportMediationStatement::tradedAt,
            ReportMediationStatement::date
            )
        .containsExactly(
            Tuple.tuple(1L, "11", "1", 1L, "lastName firstName", "2022-02-02 11:02:01", toDate("2022/02/02 02:02:01").getTime()),
            Tuple.tuple(2L, "12", "2", 1L, "lastName firstName", "2022-02-02 11:02:02", toDate("2022/02/02 02:02:02").getTime()),
            Tuple.tuple(3L, "13", "3", 2L, "corporateName", "2022-02-02 11:02:11", toDate("2022/02/02 02:02:11").getTime()),
            Tuple.tuple(4L, "14", "4", 2L, "corporateName", "2022-02-02 11:02:12", toDate("2022/02/02 02:02:12").getTime())
        );

    assertThat(writeDtos)
        .as("媒介又は代理の別")
        .extracting(
            ReportMediationStatement::mediationType
        )
        .containsOnly("媒介");

    assertThat(writeDtos)
        .as("仮想通貨の名称, 媒介又は代理の内容, 仮想通貨の数量, 約定価格又は単価及び金額, 媒介又は代理に関して受け取る手数料、報酬その他の対価の額")
        .extracting(
            ReportMediationStatement::currency,
            ReportMediationStatement::mediationDetail,
            ReportMediationStatement::tradeAmount, // 仮想通貨の数量
            ReportMediationStatement::tradePrice, // 約定価格又は単価及び金額
            ReportMediationStatement::otherFee
        )
        .containsExactly(
            Tuple.tuple("ADA", "板取引(買い)", "2.000", "1.000", "4"),
            Tuple.tuple("ADA", "板取引(売り)", "6.000", "5.000", "8"),
            Tuple.tuple("ADA", "板取引(買い)", "12.000", "11.000", "14"),
            Tuple.tuple("ADA", "板取引(売り)", "16.000", "15.000", "18")
        );
  }
}