package exchange.admin.controller;

import java.util.List;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.admin.entity.AdminUser;
import exchange.common.constant.ErrorCode;
import exchange.common.entity.PosControl;
import exchange.common.entity.PosMMConfig;
import exchange.common.exception.CustomException;
import exchange.common.model.request.PosControlForm;
import exchange.common.model.request.PosMMConfigForm;
import exchange.common.service.PosControlService;
import exchange.common.service.PosMMConfigService;
import lombok.RequiredArgsConstructor;
@RequiredArgsConstructor
@RequestMapping("/admin/v1/pos/mmconfig")
@RestController
public class V1PosMMConfigController extends ExchangeAdminController{

    private final PosMMConfigService posMMConfigService;
    private final PosControlService posControlService;
    
    @GetMapping("/{id}")
    public ResponseEntity<PosMMConfig> getConfig(@AuthenticationPrincipal AdminUser adminUser,@PathVariable Long id) throws CustomException {
        if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
            throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
        }
        return ResponseEntity.ok(posMMConfigService.getConfig(id));
    }

    @GetMapping("/all")
    public ResponseEntity<List<PosMMConfig>> getAllConfig(@AuthenticationPrincipal AdminUser adminUser,@RequestParam(value = "symbolId", required = false) Long symbolId) throws CustomException {
        if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
            throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
        }
        return ResponseEntity.ok(posMMConfigService.getAllConfig(symbolId));
    }

    @PutMapping
    public ResponseEntity<PosMMConfig> modifyConfig(@AuthenticationPrincipal AdminUser adminUser,@RequestBody PosMMConfigForm form)
        throws Exception {
        if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
            throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
        }
        return ResponseEntity.ok(posMMConfigService.editConfig(form));
    }
    
    @GetMapping("/control")
    public ResponseEntity<PosControl> get(@AuthenticationPrincipal AdminUser adminUser)
        throws Exception {
        if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
            throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
        }
        PosControl posControl = posControlService.findOne(1L);
        return ResponseEntity.ok(posControl);
    }
    
    @PutMapping("/control")
    public ResponseEntity<PosControl> update(@AuthenticationPrincipal AdminUser adminUser,@RequestBody PosControlForm form)
        throws Exception {
        if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
            throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
        }
        PosControl posControl = posControlService.findOne(1L);
        posControl.setDisplayFlg(form.isDisplayFlg());
        posControl.setOperatorId(adminUser.getId().toString());
        return ResponseEntity.ok(posControlService.save(posControl));
    }
}
