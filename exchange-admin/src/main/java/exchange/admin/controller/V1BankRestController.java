package exchange.admin.controller;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.Iterator;
import java.util.TreeSet;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.admin.entity.AdminUser;
import exchange.admin.model.request.BankCsvPostForm;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.ViewVariables;
import exchange.common.entity.Bank;
import exchange.common.exception.CustomException;
import exchange.common.model.response.PageData;
import exchange.common.service.BankService;
import lombok.RequiredArgsConstructor;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/bank")
public class V1BankRestController extends ExchangeAdminController {

  private final BankService bankService;

  @GetMapping
  public ResponseEntity<PageData<Bank>> get(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "bankName", required = false) String bankName,
      @RequestParam(value = "bankCode", required = false) Integer bankCode,
      @RequestParam(value = "branchName", required = false) String branchName,
      @RequestParam(value = "branchCode", required = false) Integer branchCode,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    return ResponseEntity.ok(
        bankService.findByConditionPageData(
            bankName, bankCode, branchName, branchCode, number, size));
  }

  @PostMapping("/csvregist")
  public ResponseEntity<Serializable> post(
      @AuthenticationPrincipal AdminUser adminUser, @Valid @RequestBody BankCsvPostForm[] res)
      throws Exception {

    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    ArrayList<Bank> banks = new ArrayList<>();
    ArrayList<Bank> insertBank = new ArrayList<>();
    ArrayList<Bank> updateBank = new ArrayList<>();

    log.info("The total number of imported CSV data is: {}",res.length);
    Long start = System.currentTimeMillis();
    for (BankCsvPostForm form  : res) {
      Integer rankCode = Integer.parseInt(form.getRankCode());
      if (rankCode != 1) {
          continue;
      }
      Integer bankCode = Integer.parseInt(form.getBankCode());
      Integer branchCode = Integer.parseInt(form.getBranchCode());
      Bank exBank = bankService.findOneByBankAndBranchCode(bankCode, branchCode);
      if (exBank == null) {
        // insert
        Bank bank = new Bank();
        bank.setBankCode(bankCode);
        bank.setBranchCode(branchCode);
        bank.setBankNameKana(form.getBankNameKana());
        bank.setBankName(form.getBankName());
        bank.setBranchNameKana(form.getBranchNameKana());
        bank.setBranchName(form.getBranchName());
        bank.setCreatedAt(new Date());
        bank.setUpdatedAt(new Date());
        insertBank.add(bank);
      } else {
        // update
        exBank.setBankNameKana(form.getBankNameKana());
        exBank.setBankName(form.getBankName());
        exBank.setBranchNameKana(form.getBranchNameKana());
        exBank.setBranchName(form.getBranchName());
        exBank.setUpdatedAt(new Date());
        updateBank.add(exBank);
      }
    }
    log.info("The total number of insert data is: {}",insertBank.size());
    log.info("The total number of update data is: {}",updateBank.size());
    Long end = System.currentTimeMillis();
    log.info("Query bank info cost time is: {}", end-start);
    Long insetStart = System.currentTimeMillis();
    ArrayList<Bank> collect = insertBank.stream()
            .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(
                    Comparator.comparing(o -> o.getBankCode() + ";" + o.getBranchCode()))),
                ArrayList::new));
    if (insertBank != null && insertBank.size() > 0) {
      customTransactionManager.execute(entityManager -> {
        batchSave(collect, entityManager);
      });
    }
    Long insertEnd = System.currentTimeMillis();
    log.info("The total number of successful inserted is: {}",collect.size());
    log.info("Insert successful bank data cost time: {}",insertEnd - insetStart);
    Long updateStart = System.currentTimeMillis();
    if(updateBank !=null && updateBank.size() > 0) {
      customTransactionManager.execute(entityManager -> {
        batchUpdate(updateBank,entityManager);
      });
    }
    Long updateEnd = System.currentTimeMillis();
    log.info("The total number of successful  update is: {}",updateBank.size());
    log.info("update successful bank data cost time: {}",updateEnd - updateStart);
    return ResponseEntity.ok(banks);
  }

   public <S> Iterable<S> batchSave(Iterable<S> var1, EntityManager em) {
      Iterator<S> iterator = var1.iterator();
      int index = 0;
      while (iterator.hasNext()){
          em.persist(iterator.next());
          index++;
          if (index % 500 == 0){
              em.flush();
              em.clear();
          }
      }
      if (index % 500 != 0){
          em.flush();
          em.clear();
      }
      return var1;
  }

  public <S> Iterable<S> batchUpdate(Iterable<S> var1, EntityManager em) {
      Iterator<S> iterator = var1.iterator();
      int index = 0;
      while (iterator.hasNext()){
          em.merge(iterator.next());
          index++;
          if (index % 500 == 0){
              em.flush();
              em.clear();
          }
      }
      if (index % 500 != 0){
          em.flush();
          em.clear();
      }
      return var1;
  }
}
