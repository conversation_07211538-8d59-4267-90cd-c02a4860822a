package exchange.admin.controller;

import java.io.Serializable;
import javax.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.admin.entity.AdminUser;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.ViewVariables;
import exchange.common.entity.ApiInfo;
import exchange.common.exception.CustomException;
import exchange.common.model.request.ApiInfoPutForm;
import exchange.common.service.ApiInfoService;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/api-info")
public class V1ApiInfoRestController extends ExchangeAdminController {

  private final ApiInfoService apiInfoService;

  @GetMapping
  public ResponseEntity<Serializable> get(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "id", required = false) Long id,
      @RequestParam(value = "userId", required = false) Long userId,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    if (id != null) {
      return ResponseEntity.ok(apiInfoService.findOne(id));
    } else {
      return ResponseEntity.ok(apiInfoService.findByCondition(number, size, userId));
    }
  }

  @PutMapping
  public ResponseEntity<ApiInfo> update(
      @AuthenticationPrincipal AdminUser adminUser, @Valid @RequestBody ApiInfoPutForm form)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    ApiInfo apiInfo = apiInfoService.findOne(form.getId());

    if (apiInfo == null) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_API_INFO_IS_NULL);
    }

    apiInfo.setLabel(form.getLabel());
    apiInfo.setEnabled(form.isEnabled());
    return ResponseEntity.ok(apiInfoService.save(apiInfo));
  }

  @DeleteMapping
  public ResponseEntity<ApiInfo> delete(
      @AuthenticationPrincipal AdminUser adminUser, @RequestParam(value = "id") Long id)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    ApiInfo apiInfo = apiInfoService.findOne(id);

    if (apiInfo == null) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_API_INFO_IS_NULL);
    }

    apiInfoService.delete(apiInfo);
    return ResponseEntity.ok(apiInfo);
  }
}
