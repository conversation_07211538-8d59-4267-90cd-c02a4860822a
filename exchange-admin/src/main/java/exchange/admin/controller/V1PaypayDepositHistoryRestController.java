package exchange.admin.controller;

import java.math.BigDecimal;
import javax.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import exchange.admin.entity.AdminUser;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.FiatDepositStatus;
import exchange.common.constant.ViewVariables;
import exchange.common.entity.FiatDeposit;
import exchange.common.entity.OnetimeBankAccount;
import exchange.common.entity.PaypayDeposit;
import exchange.common.exception.CustomException;
import exchange.common.model.request.PaypayDepositPostForm;
import exchange.common.model.response.PageData;
import exchange.common.service.OnetimeBankAccountService;
import exchange.common.service.PaypayDepositService;
import exchange.common.util.DateUnit;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/paypay-deposit-history")
public class V1PaypayDepositHistoryRestController extends ExchangeAdminController {

  private final PaypayDepositService paypayDepositService;
  private final OnetimeBankAccountService onetimeBankAccountService;

  @GetMapping
  public ResponseEntity<PageData<PaypayDeposit>> get(
      @AuthenticationPrincipal AdminUser adminUser,
      @RequestParam(value = "noFiatDepositId", required = false) Boolean noFiatDepositId,
      @RequestParam(value = "fiatDepositId", required = false) Long fiatDepositId,
      @RequestParam(value = "dateFrom", required = false) Long dateFrom,
      @RequestParam(value = "dateTo", required = false) Long dateTo,
      @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
          Integer number,
      @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE) Integer size)
      throws Exception {
    if (!(hasAdminAuthority(adminUser)
        || hasUpdateAuthority(adminUser)
        || hasViewerAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;
    return ResponseEntity.ok(
        paypayDepositService.findByConditionPageData(
            noFiatDepositId, fiatDepositId, dateFrom, dateTo, number, size));
  }

  @GetMapping("/{id}")
  public ResponseEntity<PaypayDeposit> get(
      @AuthenticationPrincipal AdminUser adminUser, @PathVariable Long id) throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }
    return ResponseEntity.ok(paypayDepositService.findOne(id));
  }

  @PostMapping("/apply")
  public ResponseEntity<PaypayDeposit> apply(
      @AuthenticationPrincipal AdminUser adminUser,
      @Valid @RequestBody PaypayDepositPostForm paypayDepositPostForm)
      throws Exception {
    if (!(hasAdminAuthority(adminUser) || hasUpdateAuthority(adminUser))) {
      throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_AUTHORITY);
    }

    PaypayDeposit paypayDeposit = paypayDepositService.findOne(paypayDepositPostForm.getId());
    String accountNumber = paypayDeposit.getDummy().substring(33, 40);
    OnetimeBankAccount onetimeBankAccount =
        onetimeBankAccountService.findOneByAccountNumber(accountNumber);

    // 日本円入金情報
    FiatDeposit fiatDeposit = new FiatDeposit();
    fiatDeposit.setOnetimeBankAccountId(onetimeBankAccount.getId());
    fiatDeposit.setUserId(onetimeBankAccount.getUserId());
    fiatDeposit.setAmount(BigDecimal.valueOf(Long.valueOf(paypayDeposit.getAmount())));
    fiatDeposit.setFee(BigDecimal.ZERO);
    fiatDeposit.setFiatDepositStatus(FiatDepositStatus.DONE);

    // コメントを更新
    paypayDeposit.setComment(paypayDepositPostForm.getComment());

    paypayDepositService.applyDeposit(paypayDeposit, fiatDeposit);

    return ResponseEntity.ok(paypayDeposit);
  }
}
