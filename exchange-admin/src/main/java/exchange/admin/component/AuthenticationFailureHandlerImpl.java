package exchange.admin.component;

import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.databind.ObjectMapper;
import exchange.admin.entity.AdminUser;
import exchange.admin.service.AdminUserLoginAttemptService;
import exchange.admin.service.AdminUserService;
import exchange.common.constant.ErrorCode;
import exchange.common.model.response.ErrorData;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Component
public class AuthenticationFailureHandlerImpl implements AuthenticationFailureHandler {

  private final AdminUserService adminUserService;

  private final AdminUserLoginAttemptService adminUserLoginAttemptService;

  @Override
  public void onAuthenticationFailure(
      HttpServletRequest request, HttpServletResponse response, AuthenticationException exception)
      throws IOException, ServletException {
    response.setStatus(HttpStatus.UNAUTHORIZED.value());
    ErrorCode errorCode = ErrorCode.REQUEST_ERROR_UNAUTHORIZED;

    if (exception.getCause() instanceof LockedException) {
      // account locked
      errorCode = ErrorCode.REQUEST_ERROR_UNAUTHORIZED_ACCOUNT_LOCKED;
    }

    response.getWriter().write(new ObjectMapper().writeValueAsString(new ErrorData(errorCode)));

    if (exception.getClass() == BadCredentialsException.class) {
      AdminUser adminUser = adminUserService.loadUserByUsername(request.getParameter("email"));

      if (adminUser != null) {
        if (adminUserLoginAttemptService.countUp(adminUser.getId())) {
          adminUser.setAccountNonLocked(false);
          adminUserService.save(adminUser);
        }
      }
    }
  }
}
