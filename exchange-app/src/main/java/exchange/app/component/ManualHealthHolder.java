package exchange.app.component;

import java.util.concurrent.atomic.AtomicBoolean;
import org.springframework.stereotype.Component;

@Component
public class ManualHealthHolder {
  private AtomicBoolean healthy = new AtomicBoolean(true);

  public void healthy() {
    healthy.set(true);
  }

  public void unhealthy() {
    healthy.set(false);
  }

  public boolean isHealthy() {
    return healthy.get();
  }
}
