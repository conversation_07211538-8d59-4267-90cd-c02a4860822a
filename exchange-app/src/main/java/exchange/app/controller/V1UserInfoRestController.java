package exchange.app.controller;

import exchange.common.constant.*;
import exchange.common.model.request.UpdateUserInfoForm;
import exchange.common.component.SesManager;
import exchange.common.service.MailNoreplyService;
import exchange.common.service.UserEkycService;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.persistence.criteria.Predicate;
import javax.validation.Valid;

import exchange.common.service.dowjones.DowJonesSamCaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import exchange.app.component.MfaManager;
import exchange.app.model.request.UserInfoPhoneNumberOtpauthPutForm;
import exchange.app.model.request.UserInfoPhoneNumberPutForm;
import exchange.common.component.CustomTransactionManager;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.ReportLabel.UserStatus;
import exchange.common.entity.User;
import exchange.common.entity.UserEkyc;
import exchange.common.entity.UserInfo;
import exchange.common.entity.UserInfoCorporate;
import exchange.common.entity.UserInfoCorporateAgent;
import exchange.common.entity.UserInfoCorporateOwner;
import exchange.common.entity.UserInfoCorporateRepresentative;
import exchange.common.entity.UserKyc;
import exchange.common.exception.CustomException;
import exchange.common.model.request.UserInfoCorporateForm;
import exchange.common.model.request.UserInfoForm;
import exchange.common.model.response.MfaTypeData;
import exchange.common.model.response.UserInfoCoporateData;
import exchange.common.model.response.ValidateData;
import exchange.common.repos.UserEkycRepository;
import exchange.common.service.UserAuthorityService;
import exchange.common.service.UserInfoCorporateAgentService;
import exchange.common.service.UserInfoCorporateOwnerService;
import exchange.common.service.UserInfoCorporateRepresentativeService;
import exchange.common.service.UserInfoCorporateService;
import exchange.common.service.UserInfoService;
import exchange.common.service.UserKycService;
import exchange.common.service.UserService;
import exchange.common.util.StringUtil;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/user-info")
@Timed
@Slf4j
public class V1UserInfoRestController {

  private final MfaManager mfaManager;

  private final UserAuthorityService userAuthorityService;

  private final UserInfoCorporateService userInfoCorporateService;

  private final UserInfoCorporateAgentService userInfoCorporateAgentService;

  private final UserInfoCorporateOwnerService userInfoCorporateOwnerService;

  private final UserInfoCorporateRepresentativeService userInfoCorporateRepresentativeService;

  private final UserInfoService userInfoService;

  private final UserService userService;

  private final UserEkycService userEkycService;

  private final UserKycService userKycService;

  private final MailNoreplyService mailNoreplyService;

  private final SesManager sesManager;

  private final CustomTransactionManager customTransactionManager;

  private final DowJonesSamCaseService dowJonesSamCaseService;

  //
  // partsCheck: ture => null以外のformパラメータのバリデーションを行う
  // partsCheck: false => 全formパラメータのバリデーションを行う
  //
  private ValidateData validateUserInfoForm(User user, UserInfoForm form, boolean partsCheck) {
    ValidateData validateData = new ValidateData();

    final var isEditing = user.getUserInfo() != null;

    if (userAuthorityService.findAuthority(user.getId(), Authority.PERSONAL) == null) {
      validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_INCORRECT_AUTHORITY.getCode());
      return validateData;
    }

    if (!partsCheck || form.getFirstName() != null) {
      if (!form.validateFirstName(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_FIRST_NAME.getCode());
      }
    }

    if (!partsCheck || form.getFirstKana() != null) {
      if (!form.validateFirstKana(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_FIRST_KANA.getCode());
      }
    }

    if (!partsCheck || form.getLastName() != null) {
      if (!form.validateLastName(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_LAST_NAME.getCode());
      }
    }

    if (!partsCheck || form.getLastKana() != null) {
      if (!form.validateLastKana(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_LAST_KANA.getCode());
      }
    }

    if (!partsCheck || form.getNationality() != null) {
      if (!form.validateNationality(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_NATIONALITY.getCode());
      }
    }

    if (!partsCheck || form.getZipCode() != null) {
      if (!form.validateZipCode(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_ZIP_CODE.getCode());
      }
    }

    if (!partsCheck || form.getPrefecture() != null) {
      if (!form.validatePrefecture(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_PREFECTURE.getCode());
      }
    }

    if (!partsCheck || form.getCity() != null) {
      if (!form.validateCity(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_CITY.getCode());
      }
    }

    if (!partsCheck || form.getAddress() != null) {
      if (!form.validateAddress(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_ADDRESS.getCode());
      }
    }

    if (!partsCheck || form.getBuilding() != null) {
      if (!form.validateBuilding(true)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_BUILDING.getCode());
      }
    }

    if (!partsCheck || form.getBirthday() != null) {
      if (!form.validateBirthday(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_BIRTHDAY.getCode());
      }
    }

    if (!partsCheck || form.getGender() != null) {
      if (!form.validateGender(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_GENDER.getCode());
      }
    }

    if (!partsCheck || form.getPhoneNumber() != null) {
      if (!form.validatePersonalPhoneNumber(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_INVALID_PERSONAL_PHONE_NUMBER.getCode());
      }
      Long countByPhoneNumber = 0L;
      if (form.validatePersonalPhoneNumber(false)) {
    	List<UserInfo> res = (List<UserInfo>) userInfoService.findOneByPhoneNumber(form.getPhoneNumber());
    	for(UserInfo userInfo : res) {
    		User users = (User) userService.findById(userInfo.getUserId());
    		if(!UserStatus.LEFT.name().equals(users.getUserStatus().name()) ) {
    			countByPhoneNumber = countByPhoneNumber + 1;
    		}
    	}
        if (countByPhoneNumber == 1 && isEditing) {
          if (!user.getUserInfo().getPhoneNumber().equals(form.getPhoneNumber())) {
            // 編集中でも自身の番号の場合はエラーとする
            validateData.getErrors()
                .add(ErrorCode.REQUEST_ERROR_USER_INFO_PHONE_NUMBER_EXISTS.getCode());
          }
        } else if (countByPhoneNumber != 0) {
          validateData.getErrors()
              .add(ErrorCode.REQUEST_ERROR_USER_INFO_PHONE_NUMBER_EXISTS.getCode());
        }
      }
    }

    if (!partsCheck || form.getOccupation() != null) {
      if (!form.validateOccupation(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_OCCUPATION.getCode());
      }

      if (!form.validateIndustry(form.notEmptyOccupation(false))) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_INDUSTRY.getCode());
      }

      if (!form.validateWorkPlace(form.canEmptyOccupation(false))) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_WORKPLACE.getCode());
      }
      if (!form.validatePosition(form.canEmptyOccupation(false))) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_POSITION.getCode());
      }
    }

    if (!partsCheck || form.getIncome() != null) {
      if (!form.validateIncome(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_INCOME.getCode());
      }
    }

    if (!partsCheck || form.getFinancialAssets() != null) {
      if (!form.validateFinancialAssets(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_FINANCIAL_ASSETS.getCode());
      }
    }

    if (!partsCheck || form.getPurpose() != null) {
      if (!form.validatePurpose(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_PURPOSE.getCode());
      }
    }

    if (!partsCheck || form.getInvestmentPurposes() != null) {
      if (!form.validateInvestmentPurposes(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_INVESTMENT_PURPOSE.getCode());
      }
    }

    if (!partsCheck || form.getCryptoExperience() != null) {
      if (!form.validateCryptoExperience(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_CRYPTO_EXPERIENCE.getCode());
      }
    }

    if (!partsCheck || form.getStocksExperience() != null) {
      if (!form.validateStocksExperience(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_STOCKS_EXPERIENCE.getCode());
      }
    }

    if (!partsCheck || form.getFundExperience() != null) {
      if (!form.validateFundExperience(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_FUND_EXPERIENCE.getCode());
      }
    }

    if (!partsCheck || form.getApplicationHistory() != null) {
      if (!form.validateApplicationHistory(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_APPLICATION_HISTORY.getCode());
      }

      if (!form.validateApplicationHistoryOther(!form.IsApplicationHistoryOther(false))) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_APPLICATION_HISTORY_OTHER.getCode());
      }
    }

    if (!partsCheck || form.getForeignPeps() != null) {
      if (!form.validateForeignPeps(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_FOREIGN_PEPS.getCode());
      }
    }

    if (!partsCheck || form.getCountry() != null) {
      if (!form.validateCountry(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_COUNTRY.getCode());
      }
    }

    return validateData;
  }

  @GetMapping("/personal")
  public ResponseEntity<UserInfo> getPersonal(@AuthenticationPrincipal User user) throws Exception {
    User currentUser = userService.findOne(user.getId());
    return ResponseEntity.ok(userInfoService.findOne(currentUser.getUserInfoId()));
  }

  private final UserEkycRepository repository;

  @PostMapping("/personal")
  public ResponseEntity<ValidateData> postPersonal(@AuthenticationPrincipal User user,
      @RequestBody UserInfoForm form) throws Exception {
    ValidateData validateData = validateUserInfoForm(user, form, false);

    if (validateData.getErrors().size() > 0) {
      return ResponseEntity.badRequest().body(validateData);
    }

    KycStatus status = user.getKycStatus();
    if (KycStatus.DOCUMENT_REJECTED == user.getKycStatus()) {
    	 List<UserEkyc> res = userEkycService.findOneByUserId(user.getId());
    	 for(UserEkyc userEkyc : res) {
    	    Date createTime = Date.from(Instant.ofEpochMilli(userEkyc.getCreatedAt().getTime()).minus(4, ChronoUnit.HOURS));
    	    userEkyc.setCreatedAt(createTime);
    	    userEkyc.setUpdatedAt(createTime);
    	    repository.save(userEkyc);
    	 }
    }

    User userForUpdate = userService.findOne(user.getId());
    final var isNew = userForUpdate.getUserInfo() == null;

    customTransactionManager.execute(entityManager -> {
      UserInfo userInfo = new UserInfo(user.getId());
      userInfo.setInsider(form.getInsider());
      if("9".equals(form.getOccupation()) || "10".equals(form.getOccupation()) || "11".equals(form.getOccupation())) {
      	form.setPosition(null);
      	form.setWorkPlace(null);
      } else {
  		form.setPriceFrom(null);
  	}
      if(!("1".equals(form.getOccupation()) || "2".equals(form.getOccupation()) || "7".equals(form.getOccupation()))) {
      	form.setIndustry(null);
      }
      userInfo = userInfoService.save(userInfo.setProperties(form), entityManager);
      final var newUserKyc = new UserKyc(userForUpdate.getId());
      newUserKyc.setKycStatus(KycStatus.DOCUMENT_WAITING_APPLY);
      newUserKyc.setOperator(CommonConstants.APP);
      newUserKyc.setUserInfoId(userInfo.getId());
      userKycService.save(newUserKyc, entityManager);
      userForUpdate.setUserInfoId(userInfo.getId());
      userForUpdate.setUserKycId(newUserKyc.getId());
      userForUpdate.setKycStatus(newUserKyc.getKycStatus());
      if (form.getInsider() != null) {
        userForUpdate.setInsider(form.getInsider());
      }
      userService.saveWithAuthenticationPrincipal(userForUpdate, entityManager);
    });

    dowJonesSamCaseService.asyncUpdateCase(user.getId());

    // Ekycのデータ作成、メール送信
    if (isNew) {
      userEkycService.createEkyc(userForUpdate.getId());
    }

    return ResponseEntity.ok().build();
  }

  @PostMapping("/personal/validate")
  public ResponseEntity<ValidateData> postPersonalValidate(@AuthenticationPrincipal User user,
      @RequestBody UserInfoForm form) throws Exception {
    ValidateData validateData = validateUserInfoForm(user, form, true);

    if (validateData.getErrors().size() > 0) {
      return ResponseEntity.badRequest().body(validateData);
    }

    return ResponseEntity.ok().build();
  }

  private ValidateData validateUserInfoCorporateForm(User user, UserInfoCorporateForm form,
      boolean partsCheck) {
    ValidateData validateData = new ValidateData();

    if (userAuthorityService.findAuthority(user.getId(), Authority.CORPORATE) == null) {
      validateData.getErrors()
          .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_INCORRECT_AUTHORITY.getCode());
      return validateData;
    }

    if (!partsCheck || form.getName() != null) {
      if (!form.validateName(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_NAME.getCode());
      }
    }

    if (!partsCheck || form.getNameKana() != null) {
      if (!form.validateNameKana(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_NAME_KANA.getCode());
      }
    }

    if (!partsCheck || form.getEstablishedYear() != null) {
      if (!form.validateEstablishedYear(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_ESTABLISHED_YEAR.getCode());
      }
    }

    if (!partsCheck || form.getEstablishedMonth() != null) {
      if (!form.validateEstablishedMonth(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_ESTABLISHED_MONTH.getCode());
      }
    }

    if (!partsCheck || form.getEstablishedDay() != null) {
      if (!form.validateEstablishedDay(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_ESTABLISHED_DAY.getCode());
      }
    }

    if (!partsCheck || form.getAccountingMonth() != null) {
      if (!form.validateAccountingMonth(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_ACCOUNTING_MONTH.getCode());
      }
    }

    if (!partsCheck || form.getPhoneNumber() != null) {
      if (!form.validatePhoneNumber(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_PHONE_NUMBER.getCode());
      }
    }

    if (!partsCheck || form.getBusinessContent() != null) {
      if (!form.validateBusinessContent(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_BUSINESS_CONTENT.getCode());
      }
    }

    if (!partsCheck || form.getSales() != null) {
      if (!form.validateSales(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_SALES.getCode());
      }
    }

    if (!partsCheck || form.getCity() != null) {
      if (!form.validateCity(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_CITY.getCode());
      }
    }

    if (!partsCheck || form.getAddress() != null) {
      if (!form.validateAddress(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_ADDRESS.getCode());
      }
    }

    if (!partsCheck || form.getBuilding() != null) {
      if (!form.validateBuilding(true)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_BUILDING.getCode());
      }
    }

    if (!partsCheck || form.getZipCode() != null) {
      if (!form.validateZipCode(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_ZIP_CODE.getCode());
      }
    }

    if (!partsCheck || form.getPrefecture() != null) {
      if (!form.validatePrefecture(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_PREFECTURE.getCode());
      }
    }

    if (!partsCheck || form.getFinancialAssets() != null) {
      if (!form.validateFinancialAssets(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_FINANCIAL_ASSETS.getCode());
      }
    }

    if (!partsCheck || form.getPurpose() != null) {
      if (!form.validatePurpose(false)) {
        validateData.getErrors().add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_PURPOSE.getCode());
      }
    }

    if (!partsCheck || form.getCryptoExperience() != null) {
      if (!form.validateCryptoExperience(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_CRYPTO_EXPERIENCE.getCode());
      }
    }

    if (!partsCheck || form.getStocksExperience() != null) {
      if (!form.validateStocksExperience(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_STOCKS_EXPERIENCE.getCode());
      }
    }

    if (!partsCheck || form.getFundExperience() != null) {
      if (!form.validateFundExperience(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_FUND_EXPERIENCE.getCode());
      }
    }

    if (!partsCheck || form.getApplicationHistory() != null) {
      if (!form.validateApplicationHistory(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_APPLICATION_HISTORY.getCode());
      }

      if (!form.validateApplicationHistoryOther(!form.IsApplicationHistoryOther(false))) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_APPLICATION_HISTORY_OTHER.getCode());
      }
    }

    // Representative validation
    UserInfoCorporateForm.Representative representative = form.getRepresentative();
    if (!partsCheck || representative != null) {
      if (!representative.validateFirstName(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_FIRST_NAME.getCode());
      }

      if (!representative.validateFirstKana(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_FIRST_KANA.getCode());
      }

      if (!representative.validateLastName(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_LAST_NAME.getCode());
      }

      if (!representative.validateLastKana(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_LAST_KANA.getCode());
      }

      if (!representative.validatePosition(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_POSITION.getCode());
      }

      if (!representative.validateNationality(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_NATIONALITY.getCode());
      }

      if (!representative.validateZipCode(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_ZIP_CODE.getCode());
      }

      if (!representative.validatePrefecture(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_PREFECTURE.getCode());
      }

      if (!representative.validateCity(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_CITY.getCode());
      }

      if (!representative.validateAddress(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_ADDRESS.getCode());
      }

      if (!representative.validateBuilding(true)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_BUILDING.getCode());
      }

      if (!representative.validateBirthday(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_BIRTHDAY.getCode());
      }

      if (!representative.validateGender(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_GENDER.getCode());
      }

      if (!representative.validatePhoneNumber(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_PHONE_NUMBER.getCode());
      }

      if (!representative.validateCountry(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_REPRESENTATIVE_COUNTRY.getCode());
      }

      if (!representative.validateForeignPeps(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_FOREIGN_PEPS.getCode());
      }
    }

    // corporate agent
    if (Boolean.parseBoolean(form.getPersonExceptRepresentative())) {
      UserInfoCorporateForm.Agent agent = form.getAgent();

      if (!agent.validateFirstName(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_FIRST_NAME.getCode());
      }

      if (!agent.validateFirstKana(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_FIRST_KANA.getCode());
      }

      if (!agent.validateLastName(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_LAST_NAME.getCode());
      }

      if (!agent.validateLastKana(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_LAST_KANA.getCode());
      }

      if (!agent.validatePosition(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_POSITION.getCode());
      }

      if (!agent.validateNationality(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_NATIONALITY.getCode());
      }

      if (!agent.validateZipCode(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_ZIP_CODE.getCode());
      }

      if (!agent.validatePrefecture(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_PREFECTURE.getCode());
      }

      if (!agent.validateCity(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_CITY.getCode());
      }

      if (!agent.validateAddress(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_ADDRESS.getCode());
      }

      if (!agent.validateBuilding(true)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_BUILDING.getCode());
      }

      if (!agent.validateBirthday(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_BIRTHDAY.getCode());
      }

      if (!agent.validateForeignPeps(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_FOREIGN_PEPS.getCode());
      }

      if (!agent.validatePhoneNumber(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_PHONE_NUMBER.getCode());
      }

      if (!agent.validateCountry(false)) {
        validateData.getErrors()
            .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_AGENT_COUNTRY.getCode());
      }
    }

    // corporate owner
    if (Boolean.parseBoolean(form.getUltimateBeneficialOwnership())) {
      for (UserInfoCorporateForm.Owner owner : form.getOwners()) {
        if (!owner.validateFirstName(false)) {
          validateData.getErrors()
              .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_FIRST_NAME.getCode());
        }

        if (!owner.validateFirstKana(false)) {
          validateData.getErrors()
              .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_FIRST_KANA.getCode());
        }

        if (!owner.validateLastName(false)) {
          validateData.getErrors()
              .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_LAST_NAME.getCode());
        }

        if (!owner.validateLastKana(false)) {
          validateData.getErrors()
              .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_LAST_KANA.getCode());
        }

        if (!owner.validatePosition(false)) {
          validateData.getErrors()
              .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_POSITION.getCode());
        }

        if (!owner.validateNationality(false)) {
          validateData.getErrors()
              .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_NATIONALITY.getCode());
        }

        if (!owner.validateZipCode(false)) {
          validateData.getErrors()
              .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_ZIP_CODE.getCode());
        }

        if (!owner.validatePrefecture(false)) {
          validateData.getErrors()
              .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_PREFECTURE.getCode());
        }

        if (!owner.validateCity(false)) {
          validateData.getErrors()
              .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_CITY.getCode());
        }

        if (!owner.validateAddress(false)) {
          validateData.getErrors()
              .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_ADDRESS.getCode());
        }

        if (!owner.validateBuilding(true)) {
          validateData.getErrors()
              .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_BUILDING.getCode());
        }

        if (!owner.validateBirthday(false)) {
          validateData.getErrors()
              .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_BIRTHDAY.getCode());
        }

        if (!owner.validateForeignPeps(false)) {
          validateData.getErrors()
              .add(ErrorCode.REQUEST_ERROR_USER_INFO_CORPORATE_OWNER_FOREIGN_PEPS.getCode());
        }
      }
    }

    return validateData;
  }

  @GetMapping("/corporate")
  public ResponseEntity<UserInfoCoporateData> getCorporate(@AuthenticationPrincipal User user)
      throws Exception {
    User currentUser = userService.findOne(user.getId());
    UserInfoCoporateData res = new UserInfoCoporateData();

    UserInfoCorporate corporate =
        userInfoCorporateService.findOne(currentUser.getUserInfoCorporateId());
    res.setCorporate(corporate);

    if (corporate != null) {
      res.setRepresentative(
          userInfoCorporateRepresentativeService.findOne(corporate.getRepresentativeId()));
      res.setAgent(userInfoCorporateAgentService.findOne(corporate.getAgentId()));

      List<UserInfoCorporateOwner> owners = new ArrayList<UserInfoCorporateOwner>();
      if (corporate.getOwnerIds() != null && !corporate.getOwnerIds().isEmpty()) {
        for (String ownerIdStr : corporate.getOwnerIds().split(",")) {
          Long ownerId = Long.valueOf(ownerIdStr);
          UserInfoCorporateOwner owner = userInfoCorporateOwnerService.findOne(ownerId);
          if (owner != null) {
            owners.add(owner);
          }
        }
      }
      res.setOwners(owners);
    } else {
      res.setRepresentative(null);
      res.setAgent(null);
      res.setOwners(new ArrayList<UserInfoCorporateOwner>());
    }

    return ResponseEntity.ok(res);
  }

  @PostMapping("/corporate")
  public ResponseEntity<ValidateData> postCorporate(@AuthenticationPrincipal User user,
      @RequestBody UserInfoCorporateForm form) throws Exception {
    ValidateData validateData = validateUserInfoCorporateForm(user, form, false);

    if (validateData.getErrors().size() > 0) {
      return ResponseEntity.badRequest().body(validateData);
    }

    User userForUpdate = userService.findOne(user.getId());

    // Register or Update representative
    UserInfoCorporateRepresentative value = new UserInfoCorporateRepresentative();
    value.setUserId(user.getId());
    value.setProperties(user.getId(), form.getRepresentative());
    userInfoCorporateRepresentativeService.save(value);
    Long representativeId = value.getId();

    // Register owners if exist
    String ownerIds = "";
    if (Boolean.parseBoolean(form.getUltimateBeneficialOwnership())) {
      List<UserInfoCorporateOwner> owners = new ArrayList<>();
      for (int i = 0; i < form.getOwners().size(); i++) {
        UserInfoCorporateOwner owner = new UserInfoCorporateOwner();
        owner.setProperties(user.getId(), form.getOwners().get(i));
        owners.add(owner);
      }
      userInfoCorporateOwnerService.saveAll(user.getId(), owners);
      ownerIds = owners.stream().map((owner) -> {
        return owner.getId().toString();
      }).collect(Collectors.joining(","));
    }

    // Register agent if exist
    Long agentId = null;
    if (Boolean.parseBoolean(form.getPersonExceptRepresentative())) {
      UserInfoCorporateAgent agent = new UserInfoCorporateAgent();
      agent.setProperties(user.getId(), form.getAgent());
      userInfoCorporateAgentService.save(agent);
      agentId = agent.getId();
    }

    // Register or Update user-info-corporate
    UserInfoCorporate corporate = new UserInfoCorporate();
    corporate.setUserId(user.getId());
    corporate.setAgentId(agentId);
    corporate.setRepresentativeId(representativeId);
    corporate.setOwnerIds(ownerIds);
    corporate.setInsider(form.getInsider());
    form.setForeignPeps(form.getRepresentative().getForeignPeps()); // foreign pepsは代表者情報ページにある
    corporate = userInfoCorporateService.save(corporate.setProperties(form));

    // UserKycデータ
    final var newUserKyc = new UserKyc(userForUpdate.getId());
    newUserKyc.setKycStatus(KycStatus.DOCUMENT_WAITING_APPLY);
    newUserKyc.setUserInfoCorporateId(corporate.getId());
    newUserKyc.setOperator(CommonConstants.APP);
    userKycService.save(newUserKyc);

    // Update user
    userForUpdate.setUserInfoCorporateId(corporate.getId());
    userForUpdate.setUserKycId(newUserKyc.getId());
    userForUpdate.setKycStatus(newUserKyc.getKycStatus());
    userForUpdate.setInsider(form.getInsider());
    userService.saveWithAuthenticationPrincipal(userForUpdate);

    dowJonesSamCaseService.asyncUpdateCase(user.getId());

    return ResponseEntity.ok().build();
  }

  @PostMapping("/corporate/validate")
  public ResponseEntity<ValidateData> postCorporateValidate(@AuthenticationPrincipal User user,
      @RequestBody UserInfoCorporateForm form) throws Exception {
    ValidateData validateData = validateUserInfoCorporateForm(user, form, true);

    if (validateData.getErrors().size() > 0) {
      return ResponseEntity.badRequest().body(validateData);
    }

    return ResponseEntity.ok().build();
  }

  private void validatePhoneNumber(User user, UserInfoPhoneNumberOtpauthPutForm form)
      throws Exception {
    if (userAuthorityService.findAuthority(user.getId(), Authority.PERSONAL) == null) {
      if (!StringUtil.validatePhoneNumber(form.getPhoneNumber())) {
        throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_PHONE_NUMBER);
      }
    } else {
      if (!StringUtil.validatePersonalPhoneNumber(form.getPhoneNumber())) {
        throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_PERSONAL_PHONE_NUMBER);
      }
    }
  }

  @PutMapping("/phone-number/otpauth")
  public ResponseEntity<MfaTypeData> putPhoneNumberOtpauth(@AuthenticationPrincipal User user,
      @Valid @RequestBody UserInfoPhoneNumberOtpauthPutForm form) throws Exception {
    validatePhoneNumber(user, form);
    return mfaManager.responseMfaTypeData(user);
  }

  @PutMapping("/phone-number")
  public ResponseEntity<Object> putPhoneNumber(@AuthenticationPrincipal User user,
      @Valid @RequestBody UserInfoPhoneNumberPutForm form) throws Exception {
    mfaManager.authenticate(user, form.getMfaCode());
    validatePhoneNumber(user, form);

    User userForUpdate = userService.findOne(user.getId());
    if (userForUpdate.getUserInfo() != null) {
      userForUpdate.getUserInfo().setPhoneNumber(form.getPhoneNumber());
      userForUpdate.getUserInfo().setCountry(Country.valueOfName(form.getCountry()));
      userInfoService.save(userForUpdate.getUserInfo());
    } else if (user.getUserInfoCorporate() != null) {
      userForUpdate.getUserInfoCorporate().setPhoneNumber(form.getPhoneNumber());
      userInfoCorporateService.save(userForUpdate.getUserInfoCorporate());
    }

    return ResponseEntity.ok().build();
  }

  @PostMapping("/updatePersonal")
  public ResponseEntity<Object> postUpdatePersonal(@AuthenticationPrincipal User user,
      @RequestBody UpdateUserInfoForm form) throws Exception {
    // verify the form information
    ValidateData validateData = validateUserInfoForm(user, form, false);
    Integer errorCode = ErrorCode.REQUEST_ERROR_USER_INFO_PHONE_NUMBER_EXISTS.getCode();
    boolean isPhoneNumber = validateData.getErrors().stream().anyMatch(i -> i.equals(errorCode));
    if (validateData.getErrors().size() > 0 && !isPhoneNumber) {
      return ResponseEntity.badRequest().body(validateData);
    }
    // query user information
    User userForUpdate = userService.findOne(user.getId());

    // check if the user info is null
    if (userInfoService.findOne(userForUpdate.getUserInfoId()) == null) {
      throw new CustomException(ErrorCode.REQUEST_ERROR_USER_INFO_NOT_FOUND);
    }
    if("9".equals(form.getOccupation()) || "10".equals(form.getOccupation()) || "11".equals(form.getOccupation())) {
    	form.setPosition(null);
    	form.setWorkPlace(null);
    } else {
		form.setPriceFrom(null);
	}
    if(!("1".equals(form.getOccupation()) || "2".equals(form.getOccupation()) || "7".equals(form.getOccupation()))) {
    	form.setIndustry(null);
    }
    userInfoService.updateUserInfo(userForUpdate,form);
    return ResponseEntity.ok(HttpStatus.OK);
  }
}
