package exchange.app.model.request;

import javax.validation.constraints.NotBlank;
import org.hibernate.validator.constraints.Length;
import lombok.Getter;
import lombok.Setter;

public class TransferAccountPostForm {

  @Getter
  @Setter
  @NotBlank(message = "currency Blank")
  private String currency;

  @Getter
  @Setter
  @Length(min = 1, max = 255, message = "label Length")
  private String label;

  @Getter
  @Setter
  @Length(min = 1, max = 255, message = "address Length")
  @NotBlank(message = "address Blank")
  private String addressForPost;

}
