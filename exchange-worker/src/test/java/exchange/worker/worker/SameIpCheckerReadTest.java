package exchange.worker.worker;

import static org.assertj.core.api.Assertions.assertThat;

import exchange.common.entity.UserLoginInfo;
import javax.persistence.EntityManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@AutoConfigureMockMvc(addFilters = false)
@ActiveProfiles({"test"})
public class SameIpCheckerReadTest extends BaseReaderTest {

  @Autowired
  private SameIpChecker checker;

  @BeforeEach
  public void beforeEach() {
    // ユーザーログイン情報テーブルをリセットする
    executeSql(masterEM, "DELETE FROM user_login_info");
  }

  @DisplayName("何も取得されない")
  @Test
  void emptyTest() {
    // **************** 初期値を設定する ****************
    // 初期データなし

    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDateLocalUtc(20220101));

    // **************** 結果を検証する ****************
    assertThat(readDto.userLoginInfos()).as("ユーザーログイン情報が空").isEmpty();
    assertThat(readDto.sameIpCheckSpanHours()).as("類似者チェックの期間チェック").isEqualTo(1);
    assertThat(readDto.sameIpThresholdCount()).as("検知閾値の値チェック").isEqualTo(3);
  }

  @DisplayName("日付境界検索テスト")
  @Test
  void dateBoundaryTest() {
    // **************** 初期値を設定する ****************
    insertUserLoginInfo(masterEM, 1L, 1L, "2021-12-31 22:59:59.999", "2023-01-01 00:00:00.000");
    insertUserLoginInfo(masterEM, 2L, 1L, "2021-12-31 23:00:00.000", "2023-01-01 00:00:00.000");
    insertUserLoginInfo(masterEM, 3L, 1L, "2021-12-31 23:59:59.999", "2023-01-01 00:00:00.000");
    insertUserLoginInfo(masterEM, 4L, 1L, "2022-01-01 00:00:00.000", "2023-01-01 00:00:00.000");

    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDateLocalUtc(20220101));

    // **************** 結果を検証する ****************
    assertThat(readDto.userLoginInfos()).as("ユーザーログイン情報が取得できる").isNotEmpty();
    assertThat(readDto.userLoginInfos()).as("ユーザーログイン情報: id")
        .extracting(UserLoginInfo::getId)
        .containsExactly(2L, 3L);
  }

  // utils
  private void insertUserLoginInfo(
      EntityManager entityManager,
      Long id,
      Long userId,
      String createdAt,
      String updatedAt) {
    final var sql = "INSERT INTO user_login_info (id, user_id, ip_address, in_japan, created_at, updated_at)\n"
        + "VALUES (" + id + ", " + userId + ", '1', true,\n"
        + "'" + createdAt + "',\n"
        + "'" + updatedAt + "');";
    executeSql(entityManager, sql);
  }
}
