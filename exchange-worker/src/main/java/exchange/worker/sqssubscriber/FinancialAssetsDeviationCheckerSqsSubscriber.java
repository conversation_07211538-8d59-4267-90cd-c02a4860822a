package exchange.worker.sqssubscriber;

import org.springframework.stereotype.Component;
import exchange.worker.component.SqsSubscriber;
import exchange.worker.worker.FinancialAssetsDeviationChecker;

@Component
public class FinancialAssetsDeviationCheckerSqsSubscriber
    extends SqsSubscriber<FinancialAssetsDeviationChecker> {

  @Override
  public Class<FinancialAssetsDeviationChecker> getWorkerClass() {
    return FinancialAssetsDeviationChecker.class;
  }
}
