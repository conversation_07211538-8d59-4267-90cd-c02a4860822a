package exchange.common.repos;

import java.util.Date;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import exchange.common.entity.StakingRewardRecord;

public interface StakingRewardRecordRepository extends JpaRepository<StakingRewardRecord,Long> {

  @Query(
      nativeQuery = true, 
      value = "select max(record_time) from staking_reward_record where currency = :currency")
  Date latestRecordTime(@Param("currency") String currency);
  
}
