package exchange.common.model.request;

import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

public class OnetimeBankAccountPostForm {

  @Getter
  @Setter
  private Long userId;

  @Getter
  @Setter
  @NotNull
  private String branchCode;

  @Getter
  @Setter
  @NotNull
  private String branchName;

  @Getter
  @Setter
  @NotNull
  private String accountNumber;

}
