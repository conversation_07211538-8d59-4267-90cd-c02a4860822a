
package exchange.common.model.response;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * TransferApply
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class TransferApply {
  @JsonAlias("applyNo")
  private String applyNo;

  @JsonAlias("transferApplyDetails")
  private List<TransferApplyDetail> transferApplyDetails;


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TransferApply {\n");

    sb.append("    applyNo: ").append(toIndentedString(applyNo)).append("\n");
    sb.append("    transferApplyDetails: ").append(toIndentedString(transferApplyDetails)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

