package exchange.common.model.response.chainalysis;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import exchange.common.constant.ChainalysisAlertLevel;
import exchange.common.util.CollectionUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wen.y
 * @date: 2024/8/11
 */
@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChainalysisWithdrawalAttemptsAlertsResponse implements Serializable {

	private List<Alert> alerts;

	@Data
	public static class Alert {
		private ChainalysisAlertLevel alertLevel;
		private String category;
		private String service;
		private String externalId;
		private BigDecimal alertAmount;
		private String exposureType;
		private String categoryId;
	}

	public ChainalysisAlertLevel getMaxAlertLevel() {
		ChainalysisAlertLevel alertLevel = ChainalysisAlertLevel.UNKNOWN;
		if (CollectionUtil.isNotEmpty(alerts)) {
			for (Alert alert : alerts) {
				if (null != alert.getAlertLevel() && alert.getAlertLevel().getRiskScore() > alertLevel.getRiskScore()) {
					alertLevel = alert.getAlertLevel();
				}
			}
		}
		return alertLevel;
	}
}
