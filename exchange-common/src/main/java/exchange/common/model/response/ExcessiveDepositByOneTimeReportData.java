package exchange.common.model.response;

import java.io.Serializable;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import exchange.common.serializer.BigDecimalSerializer;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)

// ID 
// ユーザーID
// メールアドレス
// 通貨
// 入庫額（暗号資産）
// 金融資産
// 年収
// 作成日時
// TMSステータス
// 対象日時
@JsonPropertyOrder({ "ID", "ユーザーID", "メールアドレス", "通貨", "入庫額（暗号資産）", "金融資産", "年収", "作成日時", "TMSステータス", "対象日時" })
public class ExcessiveDepositByOneTimeReportData implements Serializable {
  private static final long serialVersionUID = 4236129338232034583L;

  @JsonProperty("ID")
  @Getter
  @Setter
  private Long id;

  @JsonProperty("ユーザーID")
  @Getter
  @Setter
  private Long userId;

  @JsonProperty("メールアドレス")
  @Getter
  @Setter
  private String email;
  
  @JsonProperty("通貨")
  @Getter
  @Setter
  private String currency;

  @JsonProperty("入庫額（暗号資産）")
  @Getter
  @Setter
//  @JsonSerialize(using = BigDecimalSerializer.class)
  private String amount;

  @JsonProperty("金融資産")
  @Getter
  @Setter
  private String financialAssets;
  
  @JsonProperty("年収")
  @Getter
  @Setter
  private String income;

  @JsonProperty("TMSステータス")
  @Getter
  @Setter
  private String tmsStatus;

  // targetAt
  @JsonProperty("対象日時")
  @Getter
  @Setter
  private String targetAt;

  @JsonProperty("作成日時")
  @Getter
  @Setter
  private String createdAt;

}
