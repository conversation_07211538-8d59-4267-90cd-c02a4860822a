package exchange.common.entity;

import java.util.Date;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import exchange.common.constant.Currency;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(StakingDateInfo.class)
public abstract class StakingDateInfo_ extends AbstractEntity_ {

  public static volatile SingularAttribute<StakingDateInfo, Currency> currency;
  public static volatile SingularAttribute<StakingDateInfo, Date> stakingDate;

  public static final String CURRENCY = "currency";
  public static final String STAKING_DATE = "stakingDate";
}
