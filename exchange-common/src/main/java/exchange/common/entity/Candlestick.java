package exchange.common.entity;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.MappedSuperclass;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import exchange.common.constant.CandlestickType;

@MappedSuperclass
@ToString(callSuper = true, doNotUseGetters = true)
public abstract class Candlestick extends AbstractEntity {

  private static final long serialVersionUID = -3693598531499363634L;

  public static String getTableName(Symbol symbol) {
    return symbol.getTradeType().toLowerCase() + "_candlestick_" + symbol.getCurrencyPair().toLowerCase();
  }

  @Getter
  @Setter
  @Column(name = "symbol_id", nullable = false)
  private Long symbolId;

  @Getter
  @Setter
  @Column(name = "candlestick_type", nullable = false)
  @Enumerated(EnumType.STRING)
  private CandlestickType candlestickType;

  @Getter
  @Setter
  @Column(name = "target_at", nullable = false, columnDefinition = "timestamp(3) not null")
  @Temporal(TemporalType.TIMESTAMP)
  private Date targetAt;

  @Getter
  @Setter
  @Column(name = "open", precision = 34, scale = 20)
  private BigDecimal open = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "high", precision = 34, scale = 20)
  private BigDecimal high = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "low", precision = 34, scale = 20)
  private BigDecimal low = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "close", precision = 34, scale = 20)
  private BigDecimal close = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "volume", precision = 34, scale = 20)
  private BigDecimal volume = BigDecimal.ZERO;

  @Getter
  @Setter
  @Column(name = "fixed", nullable = false)
  private boolean fixed = false;

  public Candlestick setProperties(Long symbolId, CandlestickType candlestickType, Date targetAt) {
    this.symbolId = symbolId;
    this.candlestickType = candlestickType;
    this.targetAt = targetAt;
    return this;
  }

  public void update(BigDecimal open, BigDecimal high, BigDecimal low, BigDecimal close, BigDecimal vol) {
    if (this.open.signum() == 0) {
      this.open = open;
    }

    if (this.high.compareTo(high) < 0) {
      this.high = high;
    }

    if (low.signum() > 0 && (this.low.signum() == 0 || this.low.compareTo(low) > 0)) {
      this.low = low;
    }

    if (close.signum() > 0) {
      this.close = close;
    }

    this.volume = this.volume.add(vol);
  }

  public void reset() {
    open = BigDecimal.ZERO;
    high = BigDecimal.ZERO;
    low = BigDecimal.ZERO;
    close = BigDecimal.ZERO;
    volume = BigDecimal.ZERO;
  }
}
