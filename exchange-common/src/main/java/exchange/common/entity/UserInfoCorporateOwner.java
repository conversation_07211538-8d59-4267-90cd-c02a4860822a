package exchange.common.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import org.apache.commons.lang3.StringUtils;
import com.ibm.icu.text.Transliterator;
import exchange.common.model.request.UserInfoCorporateForm;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@NoArgsConstructor
@Table(name = "user_info_corporate_owner")
@ToString(callSuper = true, doNotUseGetters = true)
public class UserInfoCorporateOwner extends AbstractEntity {

  private static final long serialVersionUID = -8955673772046322349L;

  @Getter
  @Setter
  @Column(name = "user_id", nullable = false)
  private Long userId;

  @Getter
  @Setter
  @Column(name = "first_name", nullable = false)
  private String firstName;

  @Getter
  @Setter
  @Column(name = "last_name", nullable = false)
  private String lastName;

  @Getter
  @Setter
  @Column(name = "first_kana", nullable = false)
  private String firstKana;

  @Getter
  @Setter
  @Column(name = "last_kana", nullable = false)
  private String lastKana;

  @Getter
  @Setter
  @Column(name = "position", nullable = false)
  private String position;

  @Getter
  @Setter
  @Column(name = "nationality", nullable = false)
  private String nationality;

  @Getter
  @Setter
  @Column(name = "zip_code", nullable = false)
  private String zipCode;

  @Getter
  @Setter
  @Column(name = "prefecture", nullable = false)
  private String prefecture;

  @Getter
  @Setter
  @Column(name = "city", nullable = false)
  private String city;

  @Getter
  @Setter
  @Column(name = "address", nullable = false)
  private String address;

  @Getter
  @Setter
  @Column(name = "building")
  private String building;

  @Getter
  @Setter
  @Column(name = "birthday", nullable = false)
  private String birthday;

  @Getter
  @Setter
  @Column(name = "foreign_peps", nullable = false)
  private boolean foreignPeps;

  public UserInfoCorporateOwner(Long userId) {
    this.userId = userId;
  }

  public UserInfoCorporateOwner setProperties(long userId, UserInfoCorporateForm.Owner owner) {
    Transliterator ts = Transliterator.getInstance("Halfwidth-Fullwidth");

    this.userId = userId;
    this.firstName = ts.transliterate(owner.getFirstName());
    this.lastName = ts.transliterate(owner.getLastName());
    this.firstKana = ts.transliterate(owner.getFirstKana());
    this.lastKana = ts.transliterate(owner.getLastKana());
    this.position = ts.transliterate(owner.getPosition());
    this.nationality = owner.getNationality();
    this.zipCode = owner.getZipCode();
    this.prefecture = owner.getPrefecture();
    this.city = ts.transliterate(owner.getCity());
    this.address = ts.transliterate(owner.getAddress());

    if (!StringUtils.isEmpty(owner.getBuilding())) {
      this.building = ts.transliterate(owner.getBuilding());
    } else {
      this.building = null;
    }
    this.birthday = owner.getBirthday();
    this.foreignPeps = Boolean.parseBoolean(owner.getForeignPeps());

    return this;
  }
}
