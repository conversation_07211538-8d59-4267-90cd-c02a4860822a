package exchange.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import exchange.common.constant.Currency;
import exchange.common.constant.StakingControlDealings;
import exchange.common.constant.StakingControlOperationStatus;
import exchange.common.constant.StakingControlOperations;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "staking_control")
@NoArgsConstructor
@ToString(callSuper = true, doNotUseGetters = true)
public class StakingControl extends AbstractEntity {

  private static final long serialVersionUID = -8572975693891497381L;
  
  @Getter
  @Setter
  @Column(name = "operation_date", nullable = false)
  @Temporal(TemporalType.TIMESTAMP)
  private Date operationDate;

  @Getter
  @Setter
  @Column(name = "currency", nullable = false)
  @Enumerated(EnumType.STRING)
  private Currency currency;
  
  @Getter
  @Setter
  @Column(name = "staking_total_apply_amount", precision = 34, scale = 20, nullable = false)
  private BigDecimal stakingTotalApplyAmount;
  
  @Getter
  @Setter
  @Column(name = "expiration_not_continue_principals", precision = 34, scale = 20, nullable = false)
  private BigDecimal expirationNotContinuePrincipals;
  
  @Getter
  @Setter
  @Column(name = "expiration_not_continue_reward", precision = 34, scale = 20, nullable = false)
  private BigDecimal expirationNotContinueReward;
  
  @Getter
  @Setter
  @Column(name = "expiration_continue_reward", precision = 34, scale = 20, nullable = false)
  private BigDecimal expirationContinueReward;
  
  @Getter
  @Setter
  @Column(name = "unstake_amount", precision = 34, scale = 20, nullable = false)
  private BigDecimal unstakeAmount;
  
  @Getter
  @Setter
  @Column(name = "stake_amount_accumulate_plan", precision = 34, scale = 20, nullable = false)
  private BigDecimal stakeAmountAccumulatePlan;
  
  @Getter
  @Setter
  @Column(name = "unstake_amount_plan", precision = 34, scale = 20, nullable = false)
  private BigDecimal unstakeAmountPlan;
  
  @Getter
  @Setter
  @Column(name = "operation")
  @Enumerated(EnumType.STRING)
  private StakingControlOperations operation;
  
  @Getter
  @Setter
  @Column(name = "operation_status")
  @Enumerated(EnumType.STRING)
  private StakingControlOperationStatus operationStatus;
  
  @Getter
  @Setter
  @Column(name = "operation_id", nullable = false)
  private Long operationId;
  
  @Getter
  @Setter
  @Column(name = "stake_run_date_flg", nullable = false)
  private boolean stakeRunDateFlg = false;
  
  @Getter
  @Setter
  @Column(name = "next_stake_target_flg", nullable = false)
  private boolean nextStakeTargetFlg = false;
  
  @Getter
  @Setter
  @Column(name = "staking_pool_amount", precision = 34, scale = 20, nullable = false)
  private BigDecimal stakingPoolAmount = new BigDecimal(0);
  
  @Getter
  @Setter
  @Column(name = "cancel_prepare_amount", precision = 34, scale = 20, nullable = true)
  private BigDecimal cancelPrepareAmount;
  
  @Getter
  @Setter
  @Column(name = "staking_control_dealing")
  @Enumerated(EnumType.STRING)
  private StakingControlDealings stakingControlDealing = StakingControlDealings.NONE;
  
  @Getter
  @Setter
  @Column(name = "unstake_amount_bak", precision = 34, scale = 20, nullable = true)
  private BigDecimal unstakeAmountBak;
  
}
