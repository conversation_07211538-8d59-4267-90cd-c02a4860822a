package exchange.common.predicate;

import exchange.common.constant.TmsStatus;
import exchange.common.entity.QuickCryptoToJpyUser;
import exchange.common.entity.QuickCryptoToJpyUser_;
import java.util.Date;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;

@Component
public class QuickCryptoToJpyUserPredicate extends EntityPredicate<QuickCryptoToJpyUser> {

  public Predicate equalUserId(CriteriaBuilder criteriaBuilder, Root<QuickCryptoToJpyUser> root,
      Long userId) {
    return criteriaBuilder.equal(root.get(QuickCryptoToJpyUser_.userId), userId);
  }

  public Predicate equalTmsStatus(
      CriteriaBuilder criteriaBuilder, Root<QuickCryptoToJpyUser> root, TmsStatus tmsStatus) {
    return criteriaBuilder.equal(root.get(QuickCryptoToJpyUser_.tmsStatus), tmsStatus);
  }

  public Predicate greaterThanOrEqualToTargetAt(CriteriaBuilder criteriaBuilder,
      Root<QuickCryptoToJpyUser> root, Date targetAt) {
    return criteriaBuilder.greaterThanOrEqualTo(root.get(QuickCryptoToJpyUser_.targetAt), targetAt);
  }

  public Predicate lessThanOrEqualToTargetAt(CriteriaBuilder criteriaBuilder,
      Root<QuickCryptoToJpyUser> root, Date targetAt) {
    return criteriaBuilder.lessThanOrEqualTo(root.get(QuickCryptoToJpyUser_.targetAt), targetAt);
  }
}
