package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import exchange.common.constant.Currency;
import exchange.common.entity.OtcConfig;
import exchange.common.entity.OtcConfig_;

@Component
public class OtcConfigPredicate extends EntityPredicate<OtcConfig> {

  public Predicate equalOtc(CriteriaBuilder criteriaBuilder, Root<OtcConfig> root,
      Currency currency) {
    return criteriaBuilder.equal(root.get(OtcConfig_.currency), currency);
  }

  public Predicate equalOtcAddress(CriteriaBuilder criteriaBuilder, Root<OtcConfig> root,
      String address) {
    return criteriaBuilder.equal(root.get(OtcConfig_.address), address);
  }

  public Predicate isEnabled(CriteriaBuilder criteriaBuilder, Root<OtcConfig> root,
      boolean enabled) {
    return enabled ? criteriaBuilder.isTrue(root.get(OtcConfig_.enabled))
        : criteriaBuilder.isFalse(root.get(OtcConfig_.enabled));
  }
}
