package exchange.common.constant;

public enum KycResultCode {
  OK("0", KycStatus.DOCUMENT_CONFIRMED),
  NG("1", KycStatus.DOCUMENT_REJECTED),
  INVALID("2", KycStatus.URL_EXPIRED); // 失効。URL有効期限切れ

  private String number;
  private KycStatus kycStatus;

  private KycResultCode(String number, KycStatus kycStatus) {
    this.number = number;
    this.kycStatus = kycStatus;
  }

  public static KycResultCode valueOfNumber(String number) {
    for (KycResultCode kycResultCode : values()) {
      if (kycResultCode.number.equals(number)) {
        return kycResultCode;
      }
    }
    return null;
  }

  public KycStatus getKycStatus() {
    return this.kycStatus;
  }
}
