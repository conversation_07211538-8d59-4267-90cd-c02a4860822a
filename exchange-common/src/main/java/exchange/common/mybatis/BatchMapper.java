package exchange.common.mybatis;

import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.function.BiConsumer;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.logging.Log;
import org.apache.ibatis.logging.LogFactory;
import org.apache.ibatis.session.SqlSession;

public interface BatchMapper<T> extends BaseMapper<T> {
    Log log = LogFactory.getLog(BatchMapper.class);

    /**
     * insert batch list
     * @param entityList
     * @return
     */
    int insertBatchSomeColumn(List<T> entityList);

    /**
     * batch insert or update
     *
     * @param entities list
     */
    default void saveOrUpdateBatch(List<T> entities) {
        saveOrUpdateBatch(entities, entities.size());
    }

    /**
     * batch insert
     * @param entityList
     * @return
     */
    default boolean saveBatch(List<T> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return false;
        }
        return saveBatch(entityList, entityList.size());
    }

    /**
     * batch insert
     * @param entityList
     * @param batchSize
     * @return
     */
    default boolean saveBatch(List<T> entityList, int batchSize) {
        String sqlStatement = this.getSqlStatement(SqlMethod.INSERT_ONE);
        return this.executeBatch(entityList, batchSize, (sqlSession, entity) -> {
            sqlSession.insert(sqlStatement, entity);
        });
    }

    default String getSqlStatement(SqlMethod sqlMethod) {
        return SqlHelper.getSqlStatement(this.getClass().getInterfaces()[0], sqlMethod);
    }

    default boolean saveOrUpdate(T entity) {
        if (null == entity) {
            return false;
        } else {
            TableInfo tableInfo = TableInfoHelper.getTableInfo(entity.getClass());
            Assert.notNull(tableInfo, "error: can not execute. because can not find cache of TableInfo for entity!", new Object[0]);
            String keyProperty = tableInfo.getKeyProperty();
            Assert.notEmpty(keyProperty, "error: can not execute. because can not find column for id from entity!", new Object[0]);
            Object idVal = tableInfo.getPropertyValue(entity, tableInfo.getKeyProperty());
            return !StringUtils.checkValNull(idVal) && !Objects.isNull(this.selectById((Serializable) idVal)) ? this.updateById(entity) > 0 ? true : false : (insert(entity) > 0 ? true : false);
        }
    }

    default boolean saveOrUpdateBatch(List<T> entityList, int batchSize) {
        if (null == entityList || entityList.size() <= 0) {
            return false;
        }
        TableInfo tableInfo = TableInfoHelper.getTableInfo(entityList.get(0).getClass());
        Assert.notNull(tableInfo, "error: can not execute. because can not find cache of TableInfo for entity!", new Object[0]);
        String keyProperty = tableInfo.getKeyProperty();
        Assert.notEmpty(keyProperty, "error: can not execute. because can not find column for id from entity!", new Object[0]);
        return SqlHelper.saveOrUpdateBatch(entityList.get(0).getClass(), this.getClass().getInterfaces()[0], log, entityList, batchSize, (sqlSession, entity) -> {
            Object idVal = tableInfo.getPropertyValue(entity, keyProperty);
            return StringUtils.checkValNull(idVal) || CollectionUtils.isEmpty(sqlSession.selectList(this.getSqlStatement(SqlMethod.SELECT_BY_ID), entity));
        }, (sqlSession, entity) -> {
            MapperMethod.ParamMap<T> param = new MapperMethod.ParamMap();
            param.put("et", entity);
            sqlSession.update(this.getSqlStatement(SqlMethod.UPDATE_BY_ID), param);
        });
    }

    default <E> boolean executeBatch(List<E> list, int batchSize, BiConsumer<SqlSession, E> consumer) {
        if (null == list || list.size() <= 0) {
            return false;
        }
        return SqlHelper.executeBatch(list.get(0).getClass(), log, list, batchSize, consumer);
    }
}
