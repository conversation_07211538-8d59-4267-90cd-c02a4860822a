package exchange.common.service;

import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import exchange.common.entity.IEOElectResult;
import exchange.common.entity.IEOElectResult_;
import exchange.common.predicate.IEOElectResultPredicate;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class IEOElectResultService extends EntityService<IEOElectResult, IEOElectResultPredicate> {

  @Override
  public Class<IEOElectResult> getEntityClass() {
    return IEOElectResult.class;
  }

  @Override
  protected void fetch(Root<IEOElectResult> root) {
    super.fetch(root);
    root.fetch(IEOElectResult_.ieoRecruitInfo, JoinType.INNER);
  }

  



}
