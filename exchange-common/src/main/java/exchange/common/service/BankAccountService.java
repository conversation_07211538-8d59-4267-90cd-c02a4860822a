package exchange.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import exchange.common.component.QueryExecutorCounter;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.AccountType;
import exchange.common.entity.BankAccount;
import exchange.common.entity.BankAccount_;
import exchange.common.model.response.PageData;
import exchange.common.predicate.BankAccountPredicate;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class BankAccountService extends EntityService<BankAccount, BankAccountPredicate> {

  @Override
  public Class<BankAccount> getEntityClass() {
    return BankAccount.class;
  }

  @Override
  protected void fetch(Root<BankAccount> root) {
    super.fetch(root);
    root.fetch(BankAccount_.user, JoinType.LEFT);
    root.fetch(BankAccount_.bank, JoinType.LEFT);
  }

  private List<Predicate> getPredicatesOfFindByCondition(
      CriteriaBuilder criteriaBuilder,
      Root<BankAccount> root,
      Long userId,
      Long bankId,
      AccountType accountType,
      String accountNumber,
      Boolean deleted) {
    List<Predicate> predicates = new ArrayList<>();

    if (userId != null) {
      predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
    }

    if (bankId != null) {
      predicates.add(predicate.equalBankId(criteriaBuilder, root, bankId));
    }

    if (accountType != null) {
      predicates.add(predicate.equalAccountType(criteriaBuilder, root, accountType));
    }

    if (accountNumber != null) {
      predicates.add(predicate.equalAccountNumber(criteriaBuilder, root, accountNumber));
    }

    if (deleted != null) {
      predicates.add(predicate.isDeleted(criteriaBuilder, root, deleted));
    }

    return predicates;
  }

  public PageData<BankAccount> findByConditionPageData(
      Long userId, Long bankId, Integer number, Integer size) {
    long count =
        customTransactionManager.count(
            getEntityClass(),
            new QueryExecutorCounter<>() {
              @Override
              public Long query() {
                return count(
                    entityManager,
                    criteriaBuilder,
                    criteriaQuery,
                    root,
                    getPredicatesOfFindByCondition(
                        criteriaBuilder, root, userId, bankId, null, null, null));
              }
            });

    return new PageData<BankAccount>(
        number,
        size,
        count,
        customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<BankAccount, List<BankAccount>>() {
              @Override
              public List<BankAccount> query() {
                List<Predicate> predicates =
                    getPredicatesOfFindByCondition(
                        criteriaBuilder, root, userId, bankId, null, null, null);
                return getResultList(
                    entityManager,
                    criteriaQuery,
                    root,
                    predicates,
                    number,
                    size,
                    criteriaBuilder.desc(root.get(BankAccount_.id)));
              }
            }));
  }

  public List<BankAccount> findAllByCondition(
      Long userId, Long bankId, AccountType accountType, String accountNumber) {
    List<BankAccount> dep =
        customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<BankAccount, List<BankAccount>>() {
              @Override
              public List<BankAccount> query() {
                List<Predicate> predicates =
                    getPredicatesOfFindByCondition(
                        criteriaBuilder, root, userId, bankId, accountType, accountNumber, false);
                return getResultList(
                    entityManager,
                    criteriaQuery,
                    root,
                    predicates,
                    criteriaBuilder.desc(root.get(BankAccount_.id)));
              }
            });
    return dep;
  }

  public List<BankAccount> findAllByCondition(Long userId, Long bankId) {
    return findAllByCondition(userId, bankId, null, null);
  }

  public List<BankAccount> findByCondition(Long userId, Long bankId, Integer number, Integer size) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<BankAccount, List<BankAccount>>() {
          @Override
          public List<BankAccount> query() {
            List<Predicate> predicates = new ArrayList<>();

            if (userId != null) {
              predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
            }

            if (bankId != null) {
              predicates.add(predicate.equalUserId(criteriaBuilder, root, bankId));
            }
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                number,
                size,
                criteriaBuilder.desc(root.get(BankAccount_.id)));
          }
        });
  }
}
