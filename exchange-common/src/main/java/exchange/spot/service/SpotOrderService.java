package exchange.spot.service;

import exchange.common.constant.CancelReason;
import exchange.common.constant.PosOrderStatus;
import exchange.common.core.tracer.TraceIdHolder;
import exchange.pos.entity.PosOrder;
import exchange.pos.entity.PosTrade;
import exchange.pos.service.PosOrderService;
import exchange.pos.service.PosTradeService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.util.CollectionUtils;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import exchange.common.component.QueryExecutorCounter;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.component.SesManager;
import exchange.common.component.RedisManager.LockParams;
import exchange.common.constant.Currency;
import exchange.common.constant.ErrorCode;
import exchange.common.constant.MailNoreplyType;
import exchange.common.constant.OrderChannel;
import exchange.common.constant.OrderOperator;
import exchange.common.constant.OrderSide;
import exchange.common.constant.OrderStatus;
import exchange.common.constant.OrderType;
import exchange.common.constant.TradeAction;
import exchange.common.constant.TradeType;
import exchange.common.entity.Asset;
import exchange.common.entity.CurrencyConfig;
import exchange.common.entity.CurrencyPairConfig;
import exchange.common.entity.Order;
import exchange.common.entity.Order_;
import exchange.common.entity.Symbol;
import exchange.common.entity.Trade;
import exchange.common.entity.User;
import exchange.common.exception.CustomException;
import exchange.common.model.request.SpotOrderForm;
import exchange.common.model.response.PageData;
import exchange.common.service.AssetService;
import exchange.common.service.CurrencyConfigService;
import exchange.common.service.CurrencyPairConfigService;
import exchange.common.service.MailNoreplyService;
import exchange.common.service.MailTemplateService;
import exchange.common.service.OrderService;
import exchange.common.service.OrderbookService;
import exchange.common.service.UserService;
import exchange.common.util.DateUnit;
import exchange.common.util.FormatUtil;
import exchange.common.util.FormatUtil.FormatPattern;
import exchange.common.util.JsonUtil;
import exchange.spot.entity.SpotOrder;
import exchange.spot.entity.SpotOrder_;
import exchange.spot.entity.SpotTrade;
import exchange.spot.model.SpotOrderRowMapper;
import exchange.spot.predicate.SpotOrderPredicate;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class SpotOrderService<
        E extends SpotOrder, P extends SpotOrderPredicate<E>, R extends SpotOrderRowMapper<E>>
    extends OrderService<E, P, R> implements ApplicationContextAware {

  private static ApplicationContext APPLICATION_CONTEXT;

  private static String getLockKey(Long userId, Currency currency) {
    return "lock:asset:" + userId + ":" + currency;
  }

  @SuppressWarnings("unchecked")
  public static <
          E extends SpotOrder, P extends SpotOrderPredicate<E>, R extends SpotOrderRowMapper<E>>
      SpotOrderService<E, P, R> getBean(Symbol symbol) {
    return (SpotOrderService<E, P, R>)
        APPLICATION_CONTEXT.getBean(
            symbol.getTradeType().toLowerCamelCase()
                + "Order"
                + symbol.getCurrencyPair().toUpperCamelCase()
                + "Service");
  }

  @Autowired private AssetService assetService;

  @Autowired private CurrencyConfigService currencyConfigService;

  @Autowired private CurrencyPairConfigService currencyPairConfigService;

  @Autowired private OrderbookService orderBookService;

  @Autowired private UserService userService;

  @Autowired private SesManager sesManager;

  @Autowired private MailNoreplyService mailNoreplyService;

  @Autowired private PosTradeService posTradeService;

  @Autowired private PosOrderService posOrderService;

  @Override
  @SuppressFBWarnings
  public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
    APPLICATION_CONTEXT = applicationContext;
  }

  public Stream<E> findOrders(
      Symbol symbol,
      List<Long> ids,
      Date updatedAtFrom,
      Date updatedAtTo) {
    final var masters = findFromMaster(
        symbol,
        ids,
        updatedAtFrom,
        updatedAtTo)
        .stream();
    final var histories = findFromHistory(
        symbol,
        ids,
        updatedAtFrom,
        updatedAtTo)
        .stream();
    return Stream.concat(masters, histories).distinct();
  }

  private List<E> findFromMaster(
      Symbol symbol,
      List<Long> ids,
      Date updatedAtFrom,
      Date updatedAtTo) {
    return new ArrayList<E>(
        customTransactionManager.find(getEntityClass(), new QueryExecutorReturner<E, List<E>>() {
          @Override
          public List<E> query() {
            List<Predicate> predicates = new ArrayList<>();

            if (ids != null) {
              predicates.add(predicate.inId(root, ids));
            }
            if (updatedAtFrom != null) {
              predicates.add(
                  predicate.greaterThanOrEqualToUpdatedAt(criteriaBuilder, root, updatedAtFrom));
            }
            if (updatedAtTo != null) {
              predicates.add(predicate.lessThanUpdatedAt(criteriaBuilder, root, updatedAtTo));
            }
            return getResultList(entityManager, criteriaQuery, root, predicates);
          }
        }));
  }

  public List<E> findFromHistory(
      Symbol symbol,
      List<Long> ids,
      Date updatedAtFrom,
      Date updatedAtTo) {
    MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();

    StringBuilder sql = new StringBuilder("select * from " + Order.getTableName(symbol));

    // 条件指定
    sql.append(" where symbol_id = :symbol_id");
    mapSqlParameterSource.addValue("symbol_id", symbol.getId());

    if (ids != null) {
      sql.append(" and id in (:ids)");
      mapSqlParameterSource.addValue("ids", ids);
    }

    if (updatedAtFrom != null) {
      sql.append(" and updated_at >= :updated_at_from");
      mapSqlParameterSource.addValue("updated_at_from", updatedAtFrom);
    }

    if (updatedAtTo != null) {
      sql.append(" and updated_at < :updated_at_to");
      mapSqlParameterSource.addValue("updated_at_to", updatedAtTo);
    }

    log.info("redshift-sql:" + sql);
    log.info("redshift-params:" + mapSqlParameterSource.getValues());

    return historicalTransactionManager.findFromHistory(
        sql.toString(), mapSqlParameterSource, newRowMapper());
  }

  /*
   * 【運用】predicates作成メソッドは共通化して使用する
   * インデックス順でaddする
   */

  // インデックス: symbol_id, order_status, order_type, order_side, user_id
  protected List<Predicate> createPredicates(
      CriteriaBuilder criteriaBuilder,
      Root<E> root,
      Long symbolId,
      OrderStatus[] orderStatuses,
      OrderType orderType,
      OrderType[] orderTypes,
      OrderType[] exceptOrderTypes,
      OrderSide orderSide,
      Long userId,
      List<Long> userIds,
      List<Long> exceptUserIds) {
    List<Predicate> predicates = new ArrayList<>();
    predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));

    if (!ArrayUtils.isEmpty(orderStatuses)) {
      predicates.add(predicate.inOrderStatus(root, orderStatuses));
    }

    if (orderType != null) {
      predicates.add(predicate.equalOrderType(criteriaBuilder, root, orderType));
    } else if (!ArrayUtils.isEmpty(orderTypes)) {
      predicates.add(predicate.inOrderType(root, orderTypes));
    } else if (!ArrayUtils.isEmpty(exceptOrderTypes)) {
      predicates.add(predicate.notInOrderType(criteriaBuilder, root, exceptOrderTypes));
    }

    if (orderSide != null) {
      predicates.add(predicate.equalOrderSide(criteriaBuilder, root, orderSide));
    }

    if (userId != null) {
      predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
    } else {
      // userIdsとexceptUserIdsは同時に利用される場合あり（重複時はexceptUserIdsが優先）
      if (userIds != null) {
        predicates.add(predicate.inUserIds(criteriaBuilder, root, userIds));
      }

      if (exceptUserIds != null) {
        predicates.add(predicate.inExceptUserIds(criteriaBuilder, root, exceptUserIds));
      }
    }

    return predicates;
  }

  private List<Predicate> createPredicatesOfFindByCondition(
      CriteriaBuilder criteriaBuilder,
      Root<E> root,
      Long symbolId,
      OrderStatus[] orderStatuses,
      OrderType orderType,
      OrderType[] orderTypes,
      OrderType[] exceptOrderTypes,
      OrderSide orderSide,
      OrderChannel orderChannel,
      Long userId,
      List<Long> userIds,
      List<Long> exceptUserIds,
      Long id,
      Long idFrom,
      Long idTo,
      Long dateFrom,
      Long dateTo) {
    List<Predicate> predicates =
        createPredicates(
            criteriaBuilder,
            root,
            symbolId,
            orderStatuses,
            orderType,
            orderTypes,
            exceptOrderTypes,
            orderSide,
            userId,
            userIds,
            exceptUserIds);

    if (orderChannel != null) {
      predicates.add(predicate.equalOrderChannel(criteriaBuilder, root, orderChannel));
    }

    if (id != null) {
      predicates.add(predicate.equalId(criteriaBuilder, root, id));
    } else {
      if (idFrom != null) {
        predicates.add(predicate.greaterThanOrEqualToId(criteriaBuilder, root, idFrom));
      }

      if (idTo != null) {
        predicates.add(predicate.lessThanOrEqualToId(criteriaBuilder, root, idTo));
      }

      if (dateFrom != null) {
        predicates.add(
            predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, new Date(dateFrom)));
      }

      if (dateTo != null) {
        predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
      }
    }

    return predicates;
  }

  public E findActiveById(Long symbolId, Long id, EntityManager entityManager) {
    return new QueryExecutorReturner<E, E>() {
      @Override
      public E query() {
        List<Predicate> predicates =
            createPredicates(
                criteriaBuilder,
                root,
                symbolId,
                OrderStatus.ACTIVE_ORDER_STATUSES,
                null,
                null,
                null,
                null,
                null,
                null,
                null);
        predicates.add(predicate.equalId(criteriaBuilder, root, id));
        return getSingleResult(entityManager, criteriaQuery, root, predicates);
      }
    }.execute(getEntityClass(), entityManager);
  }

  public E findActiveById(Long symbolId, Long id) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, E>() {
          @Override
          public E query() {
            List<Predicate> predicates =
                createPredicates(
                    criteriaBuilder,
                    root,
                    symbolId,
                    OrderStatus.ACTIVE_ORDER_STATUSES,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null);
            predicates.add(predicate.equalId(criteriaBuilder, root, id));
            return getSingleResult(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public List<E> findActive(Long symbolId, Long userId) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, List<E>>() {
          @Override
          public List<E> query() {
            List<Predicate> predicates =
                createPredicates(
                    criteriaBuilder,
                    root,
                    symbolId,
                    OrderStatus.ACTIVE_ORDER_STATUSES,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null);

            if (userId != null) {
              predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
            }

            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                criteriaBuilder.asc(root.get(SpotOrder_.id)));
          }
        });
  }

  public List<E> findInactive(Long symbolId, int limit) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, List<E>>() {
          @Override
          public List<E> query() {
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                createPredicates(
                    criteriaBuilder,
                    root,
                    symbolId,
                    OrderStatus.INACTIVE_ORDER_STATUSES,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null),
                0,
                    limit,
                criteriaBuilder.asc(root.get(SpotOrder_.id)));
          }
        });
  }

  public List<E> findForOrderbook(Long symbolId) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, List<E>>() {
          @Override
          public List<E> query() {
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                createPredicates(
                    criteriaBuilder,
                    root,
                    symbolId,
                    OrderStatus.ACTIVE_ORDER_STATUSES,
                    OrderType.LIMIT,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null),
                criteriaBuilder.asc(root.get(SpotOrder_.price)),
                criteriaBuilder.asc(root.get(SpotOrder_.id)));
          }
        });
  }

  public List<E> findByCondition(
      Long symbolId, OrderSide orderSide, BigDecimal priceFrom, BigDecimal priceTo) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, List<E>>() {
          @Override
          public List<E> query() {
            List<Predicate> predicates =
                createPredicates(
                    criteriaBuilder,
                    root,
                    symbolId,
                    OrderStatus.ACTIVE_ORDER_STATUSES,
                    OrderType.LIMIT,
                    null,
                    null,
                    orderSide,
                    null,
                    null,
                    null);
            predicates.add(
                predicate.betweenPrice(
                    criteriaBuilder,
                    root,
                    priceFrom == null ? BigDecimal.valueOf(Double.MIN_VALUE) : priceFrom,
                    priceTo == null ? BigDecimal.valueOf(Double.MAX_VALUE) : priceTo));
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                orderSide.isSell()
                    ? criteriaBuilder.asc(root.get(SpotOrder_.price))
                    : criteriaBuilder.desc(root.get(SpotOrder_.price)),
                criteriaBuilder.asc(root.get(SpotOrder_.id)));
          }
        });
  }

  // private List<Predicate> getPredicatesOfFindByCondition(
  // CriteriaBuilder criteriaBuilder,
  // Root<E> root,
  // Long symbolId,
  // Long userId,
  // Long id,
  // Long idFrom,
  // Long idTo,
  // Long dateFrom,
  // Long dateTo,
  // OrderStatus orderStatus,
  // OrderType orderType,
  // OrderSide orderSide,
  // Integer number,
  // Integer size) {
  // List<Predicate> predicates = new ArrayList<>();
  //
  // if (userId != null) {
  // predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
  // }
  //
  // if (id != null) {
  // predicates.add(predicate.equalId(criteriaBuilder, root, id));
  // } else {
  // if (idFrom != null) {
  // predicates.add(predicate.greaterThanOrEqualToId(criteriaBuilder, root, idFrom));
  // }
  //
  // if (idTo != null) {
  // predicates.add(predicate.lessThanId(criteriaBuilder, root, idTo));
  // }
  //
  // if (dateFrom != null) {
  // predicates.add(
  // predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, new Date(dateFrom)));
  // }
  //
  // if (dateTo != null) {
  // predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, new Date(dateTo)));
  // }
  //
  // if (orderStatus != null) {
  // predicates.add(predicate.equalOrderStatus(criteriaBuilder, root, orderStatus));
  // }
  //
  // if (orderType != null) {
  // predicates.add(predicate.equalOrderType(criteriaBuilder, root, orderType));
  // }
  //
  // if (orderSide != null) {
  // predicates.add(predicate.equalOrderSide(criteriaBuilder, root, orderSide));
  // }
  // }
  //
  // return predicates;
  // }

  public PageData<E> createPageData(List<E> content, Long count, Integer number, Integer size) {
    List<E> pageContents = new ArrayList<E>();

    int maxSize = (number * size + size) > content.size() ? content.size() : (number * size + size);

    for (int i = number * size; i < maxSize; i++) {

      pageContents.add(content.get(i));
    }

    return new PageData<E>(number, size, count, pageContents);
  }

  public PageData<E> findByConditionPageData(
      Long symbolId,
      Long userId,
      Long id,
      Long idFrom,
      Long idTo,
      Long dateFrom,
      Long dateTo,
      List<OrderStatus> orderStatuses,
      OrderType orderType,
      OrderSide orderSide,
      Integer number,
      Integer size) {
    long count =
        customTransactionManager.count(
            getEntityClass(),
            new QueryExecutorCounter<>() {
              @Override
              public Long query() {
                return count(
                    entityManager,
                    criteriaBuilder,
                    criteriaQuery,
                    root,
                    createPredicatesOfFindByCondition(
                        criteriaBuilder,
                        root,
                        symbolId,
                        CollectionUtils.isEmpty(orderStatuses)
                            ? null
                            : orderStatuses.toArray(new OrderStatus[orderStatuses.size()]),
                        orderType,
                        null,
                        null,
                        orderSide,
                        null,
                        userId,
                        null,
                        null,
                        id,
                        idFrom,
                        idTo,
                        dateFrom,
                        dateTo));
              }
            });
    return new PageData<E>(
        number,
        size,
        count,
        customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<E, List<E>>() {
              @Override
              public List<E> query() {
                List<Predicate> predicates =
                    createPredicatesOfFindByCondition(
                        criteriaBuilder,
                        root,
                        symbolId,
                        CollectionUtils.isEmpty(orderStatuses)
                            ? null
                            : orderStatuses.toArray(new OrderStatus[orderStatuses.size()]),
                        orderType,
                        null,
                        null,
                        orderSide,
                        null,
                        userId,
                        null,
                        null,
                        id,
                        idFrom,
                        idTo,
                        dateFrom,
                        dateTo);
                return getResultList(
                    entityManager,
                    criteriaQuery,
                    root,
                    predicates,
                    number,
                    size,
                    criteriaBuilder.desc(root.get(Order_.id)));
              }
            }));
  }

  public List<E> findAllByCondition(
      Long symbolId,
      Long userId,
      Long id,
      Long idFrom,
      Long idTo,
      Long dateFrom,
      Long dateTo,
      List<OrderStatus> orderStatuses,
      OrderType orderType,
      OrderSide orderSide) {
    return new ArrayList<E>(
        customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<E, List<E>>() {
              @Override
              public List<E> query() {
                List<Predicate> predicates =
                    createPredicatesOfFindByCondition(
                        criteriaBuilder,
                        root,
                        symbolId,
                        CollectionUtils.isEmpty(orderStatuses)
                            ? null
                            : orderStatuses.toArray(new OrderStatus[orderStatuses.size()]),
                        orderType,
                        null,
                        null,
                        orderSide,
                        null,
                        userId,
                        null,
                        null,
                        id,
                        idFrom,
                        idTo,
                        dateFrom,
                        dateTo);
                return getResultList(
                    entityManager,
                    criteriaQuery,
                    root,
                    predicates,
                    criteriaBuilder.desc(root.get(Order_.id)));
              }
            }));
  }

  public List<E> findAllByCondition(
      Long symbolId,
      List<Long> userIds,
      List<Long> exceptUserIds,
      Long id,
      Long idFrom,
      Long idTo,
      Long dateFrom,
      Long dateTo,
      List<OrderStatus> orderStatuses,
      OrderType orderType,
      OrderSide orderSide,
      OrderChannel orderChannel) {
    return new ArrayList<E>(
        customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<E, List<E>>() {
              @Override
              public List<E> query() {
                List<Predicate> predicates =
                    createPredicatesOfFindByCondition(
                        criteriaBuilder,
                        root,
                        symbolId,
                        CollectionUtils.isEmpty(orderStatuses)
                            ? null
                            : orderStatuses.toArray(new OrderStatus[orderStatuses.size()]),
                        orderType,
                        null,
                        null,
                        orderSide,
                        orderChannel,
                        null,
                        userIds,
                        exceptUserIds,
                        id,
                        idFrom,
                        idTo,
                        dateFrom,
                        dateTo);
                return getResultList(
                    entityManager,
                    criteriaQuery,
                    root,
                    predicates,
                    criteriaBuilder.desc(root.get(Order_.id)));
              }
            }));
  }

  public List<E> findByCondition(
      Long symbolId,
      Long userId,
      Long id,
      Date dateFrom,
      Date dateTo,
      List<OrderStatus> orderStatuses,
      EntityManager entityManager) {
    return new QueryExecutorReturner<E, List<E>>() {
      @Override
      public List<E> query() {
        return getResultList(
            entityManager,
            criteriaQuery,
            root,
            createPredicatesOfFindByCondition(
                criteriaBuilder,
                root,
                symbolId,
                CollectionUtils.isEmpty(orderStatuses)
                    ? null
                    : orderStatuses.toArray(new OrderStatus[orderStatuses.size()]),
                null,
                null,
                null,
                null,
                null,
                userId,
                null,
                null,
                id,
                null,
                null,
                dateFrom != null ? dateFrom.getTime() : null,
                dateTo != null ? dateTo.getTime() : null),
            0,
            Integer.MAX_VALUE,
            criteriaBuilder.asc(root.get(Order_.id)));
      }
    }.execute(getEntityClass(), entityManager);
  }

  // call findCondition with default sort: isAscending = true
  public List<E> findByCondition(
      Long symbolId,
      Long userId,
      Long id,
      Long idFrom,
      Long idTo,
      Long dateFrom,
      Long dateTo,
      List<OrderStatus> orderStatuses,
      OrderType orderType,
      OrderSide orderSide,
      Integer number,
      Integer size) {
    return findByCondition(
        symbolId,
        userId,
        id,
        idFrom,
        idTo,
        dateFrom,
        dateTo,
        orderStatuses,
        orderType,
        null,
        null,
        orderSide,
        number,
        size,
        true);
  }

  public List<E> findByCondition(
          Long symbolId,
          Long userId,
          Integer number,
          Integer size,
          boolean isAscending,
          CancelReason cancelReason) {
    return customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<>() {
              @Override
              public List<E> query() {

                List<Predicate> predicates = new ArrayList<>(3);
                predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));
                predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
                predicates.add(predicate.equalCancelReason(criteriaBuilder, root, cancelReason));

                return getResultList(
                        entityManager,
                        criteriaQuery,
                        root,
                        predicates,
                        number,
                        size,
                        isAscending
                                ? criteriaBuilder.asc(root.get(Order_.id))
                                : criteriaBuilder.desc(root.get(Order_.id)));
              }
            });
  }

  public List<E> findByCondition(
      Long symbolId,
      Long userId,
      Long id,
      Long idFrom,
      Long idTo,
      Long dateFrom,
      Long dateTo,
      List<OrderStatus> orderStatuses,
      OrderType orderType,
      List<OrderType> orderTypes,
      List<OrderType> exceptOrderTypes,
      OrderSide orderSide,
      Integer number,
      Integer size,
      boolean isAscending) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, List<E>>() {
          @Override
          public List<E> query() {
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                createPredicatesOfFindByCondition(
                    criteriaBuilder,
                    root,
                    symbolId,
                    CollectionUtils.isEmpty(orderStatuses)
                        ? null
                        : orderStatuses.toArray(new OrderStatus[orderStatuses.size()]),
                    orderType,
                    CollectionUtils.isEmpty(orderTypes)
                        ? null
                        : orderTypes.toArray(new OrderType[orderTypes.size()]),
                    CollectionUtils.isEmpty(exceptOrderTypes)
                        ? null
                        : exceptOrderTypes.toArray(new OrderType[exceptOrderTypes.size()]),
                    orderSide,
                    null,
                    userId,
                    null,
                    null,
                    id,
                    idFrom,
                    idTo,
                    dateFrom,
                    dateTo),
                number,
                size,
                isAscending
                    ? criteriaBuilder.asc(root.get(Order_.id))
                    : criteriaBuilder.desc(root.get(Order_.id)));
          }
        });
  }

  public List<E> findByCondition(
      Long symbolId,
      Long userId,
      Long id,
      Long idFrom,
      Long idTo,
      Long dateFrom,
      Long dateTo,
      List<OrderStatus> orderStatuses,
      OrderType orderType,
      OrderSide orderSide,
      Integer number,
      Integer size,
      boolean isAscending,
      EntityManager entityManager) {
    return new QueryExecutorReturner<E, List<E>>() {
      @Override
      public List<E> query() {
        return getResultList(
            entityManager,
            criteriaQuery,
            root,
            createPredicatesOfFindByCondition(
                criteriaBuilder,
                root,
                symbolId,
                CollectionUtils.isEmpty(orderStatuses)
                    ? null
                    : orderStatuses.toArray(new OrderStatus[orderStatuses.size()]),
                orderType,
                null,
                null,
                orderSide,
                null,
                userId,
                null,
                null,
                id,
                idFrom,
                idTo,
                dateFrom,
                dateTo),
            number,
            size,
            isAscending
                ? criteriaBuilder.asc(root.get(Order_.id))
                : criteriaBuilder.desc(root.get(Order_.id)));
      }
    }.execute(getEntityClass(), entityManager);
  }

  public BigDecimal getBestPrice(Long symbolId, OrderSide orderSide) {
    E spotOrder = findBestSpotOrder(symbolId, orderSide);
    return spotOrder == null ? null : spotOrder.getPrice();
  }

  public E findCrossBuySpotOrderCreatedBeforeWithMaxPrice(Long symbolId, BigDecimal borderPrice, Date createdAt) {
    return customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<E, E>() {
              @Override
              public E query() {
                List<Predicate> predicates =
                        createPredicates(
                                criteriaBuilder,
                                root,
                                symbolId,
                                OrderStatus.ACTIVE_ORDER_STATUSES,
                                OrderType.LIMIT,
                                null,
                                null,
                                OrderSide.BUY,
                                null,
                                null,
                                null);

                if (borderPrice != null) {
                  predicates.add(predicate.greaterThanOrEqualToPrice(criteriaBuilder, root, borderPrice));
                }
                if (createdAt != null) {
                  predicates.add(predicate.lessThanCreatedAt(criteriaBuilder, root, createdAt));
                }
                return getSingleResult(
                        entityManager,
                        criteriaQuery,
                        root,
                        predicates,
                        criteriaBuilder.desc(root.get(SpotOrder_.price)),
                        criteriaBuilder.asc(root.get(SpotOrder_.createdAt)),
                        criteriaBuilder.asc(root.get(SpotOrder_.id)));
              }
            });
  }

  public E findCrossBuySpotOrderCreatedAfterWithClosestTimestampAndMaxPrice(Long symbolId, BigDecimal borderPrice, Date createdAt) {
    return customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<E, E>() {
              @Override
              public E query() {
                List<Predicate> predicates =
                        createPredicates(
                                criteriaBuilder,
                                root,
                                symbolId,
                                OrderStatus.ACTIVE_ORDER_STATUSES,
                                OrderType.LIMIT,
                                null,
                                null,
                                OrderSide.BUY,
                                null,
                                null,
                                null);

                if (borderPrice != null) {
                  predicates.add(predicate.greaterThanOrEqualToPrice(criteriaBuilder, root, borderPrice));
                }
                if (createdAt != null) {
                  predicates.add(predicate.greaterThanOrEqualToCreatedAt(criteriaBuilder, root, createdAt));
                }
                return getSingleResult(
                        entityManager,
                        criteriaQuery,
                        root,
                        predicates,
                        criteriaBuilder.asc(root.get(SpotOrder_.createdAt)),
                        criteriaBuilder.asc(root.get(SpotOrder_.id)),
                        criteriaBuilder.desc(root.get(SpotOrder_.price))
                );
              }
            });
  }
  public E findBestSpotOrder(Long symbolId, OrderSide orderSide, Long... ids) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, E>() {
          @Override
          public E query() {
            List<Predicate> predicates =
                createPredicates(
                    criteriaBuilder,
                    root,
                    symbolId,
                    OrderStatus.ACTIVE_ORDER_STATUSES,
                    OrderType.LIMIT,
                    null,
                    null,
                    orderSide,
                    null,
                    null,
                    null);

            if (ids != null && ids.length > 0) {
              predicates.add(predicate.notInId(root, ids));
            }

            return getSingleResult(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                orderSide.isSell()
                    ? criteriaBuilder.asc(root.get(SpotOrder_.price))
                    : criteriaBuilder.desc(root.get(SpotOrder_.price)),
                criteriaBuilder.asc(root.get(SpotOrder_.createdAt)),
                criteriaBuilder.asc(root.get(SpotOrder_.id)));
          }
        });
  }

  public List<E> findMarketOrders(Long symbolId) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, List<E>>() {
          @Override
          public List<E> query() {
            List<Predicate> predicates =
                createPredicates(
                    criteriaBuilder,
                    root,
                    symbolId,
                    OrderStatus.ACTIVE_ORDER_STATUSES,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null);
            predicates.add(predicate.inOrderType(root, OrderType.MARKET, OrderType.SIMPLE_MARKET));
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                criteriaBuilder.asc(root.get(SpotOrder_.createdAt)),
                criteriaBuilder.asc(root.get(SpotOrder_.id)));
          }
        });
  }

  public List<E> findActiveStopOrders(Long symbolId, OrderSide orderSide) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<E, List<E>>() {
          @Override
          public List<E> query() {
            // 売り逆指値の最高値から処理(価格降順sort/同値はcreate昇順)のためのsort &
            // 買い逆指値の最安値から処理(価格昇順sort/同値はcreate昇順)のためのsort
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                createPredicates(
                    criteriaBuilder,
                    root,
                    symbolId,
                    OrderStatus.ACTIVE_ORDER_STATUSES,
                    OrderType.STOP,
                    null,
                    null,
                    orderSide,
                    null,
                    null,
                    null),
                orderSide.isSell()
                    ? criteriaBuilder.desc(root.get(SpotOrder_.price))
                    : criteriaBuilder.asc(root.get(SpotOrder_.price)),
                criteriaBuilder.asc(root.get(SpotOrder_.createdAt)),
                criteriaBuilder.asc(root.get(SpotOrder_.id)));
          }
        });
  }

  private StringBuilder createSqlString(
      MapSqlParameterSource mapSqlParameterSource,
      Symbol symbol,
      Long userId,
      List<Long> userIds,
      List<Long> exceptUserIds,
      Long id,
      Long idFrom,
      Long idTo,
      Date createdAtFrom,
      Date createdAtTo,
      List<OrderStatus> orderStatuses,
      OrderSide orderSide,
      OrderType orderType,
      List<OrderType> orderTypes,
      List<OrderType> exceptOrderTypes,
      OrderChannel orderChannel) {

    StringBuilder sql = new StringBuilder("select * from " + Order.getTableName(symbol));

    // 条件指定
    sql.append(" where symbol_id = :symbol_id");
    mapSqlParameterSource.addValue("symbol_id", symbol.getId());

    if (id != null) {
      sql.append(" and id = :id");
      mapSqlParameterSource.addValue("id", id);
    } else {

      if (userId != null) {
        sql.append(" and user_id = :user_id");
        mapSqlParameterSource.addValue("user_id", userId);
      } else {
        if (userIds != null) {
          sql.append(" and user_id in (:user_ids)");
          mapSqlParameterSource.addValue("user_ids", userIds);
        }

        if (exceptUserIds != null) {
          sql.append(" and user_id not in (:except_user_ids)");
          mapSqlParameterSource.addValue("except_user_ids", exceptUserIds);
        }
      }

      if (idFrom != null) {
        sql.append(" and id >= :id_from");
        mapSqlParameterSource.addValue("id_from", idFrom);
      }

      if (idTo != null) {
        sql.append(" and id <= :id_to");
        mapSqlParameterSource.addValue("id_to", idTo);
      }

      if (createdAtFrom != null) {
        sql.append(" and created_at >= :created_at_from");
        mapSqlParameterSource.addValue("created_at_from", createdAtFrom);
      }

      if (createdAtTo != null) {
        sql.append(" and created_at < :created_at_to");
        mapSqlParameterSource.addValue("created_at_to", createdAtTo);
      }

      if (orderStatuses != null && !orderStatuses.isEmpty()) {
        sql.append(" and order_status in (:order_statuses)");
        mapSqlParameterSource.addValue(
            "order_statuses",
            orderStatuses
                .stream()
                .map((orderStatus) -> orderStatus.name())
                .collect(Collectors.toList()));
      }

      if (orderSide != null) {
        sql.append(" and order_side = :order_side");
        mapSqlParameterSource.addValue("order_side", orderSide.toString());
      }

      if (orderType != null) {
        sql.append(" and order_type = :order_type");
        mapSqlParameterSource.addValue("order_type", orderType.toString());
      } else {
        if (!CollectionUtils.isEmpty(orderTypes)) {
          sql.append(" and order_type in (:order_types)");
          mapSqlParameterSource.addValue(
              "order_types",
              orderTypes.stream().map((type) -> type.name()).collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(exceptOrderTypes)) {
          sql.append(" and order_type not in (:except_order_types)");
          mapSqlParameterSource.addValue(
              "except_order_types",
              exceptOrderTypes.stream().map((type) -> type.name()).collect(Collectors.toList()));
        }
      }

      if (orderChannel != null) {
        sql.append(" and order_channel = :order_channel");
        mapSqlParameterSource.addValue("order_channel", orderChannel.toString());
      }
    }

    return sql;
  }

  public List<E> findAllFromHistory(
      Symbol symbol,
      Long userId,
      Long id,
      Long idFrom,
      Long idTo,
      Date createdAtFrom,
      Date createdAtTo,
      List<OrderStatus> orderStatuses,
      OrderType orderType,
      List<OrderType> orderTypes,
      List<OrderType> exceptOrderTypes,
      OrderSide orderSide) {
    MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();

    StringBuilder sql =
        createSqlString(
            mapSqlParameterSource,
            symbol,
            userId,
            null,
            null,
            id,
            idFrom,
            idTo,
            createdAtFrom,
            createdAtTo,
            orderStatuses,
            orderSide,
            orderType,
            orderTypes,
            exceptOrderTypes,
            null);

    log.info("redshift-sql:" + sql.toString());
    log.info("redshift-params:" + mapSqlParameterSource.getValues().toString());

    return historicalTransactionManager.findFromHistory(
        sql.toString(), mapSqlParameterSource, newRowMapper());
  }

  public List<E> findAllFromHistory(
      Symbol symbol,
      List<Long> userIds,
      List<Long> exceptUserIds,
      Long id,
      Long idFrom,
      Long idTo,
      Date createdAtFrom,
      Date createdAtTo,
      List<OrderStatus> orderStatuses,
      OrderType orderType,
      OrderSide orderSide,
      OrderChannel orderChannel) {
    MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();

    StringBuilder sql =
        createSqlString(
            mapSqlParameterSource,
            symbol,
            null,
            userIds,
            exceptUserIds,
            id,
            idFrom,
            idTo,
            createdAtFrom,
            createdAtTo,
            orderStatuses,
            orderSide,
            orderType,
            null,
            null,
            orderChannel);

    log.info("redshift-sql:" + sql.toString());
    log.info("redshift-params:" + mapSqlParameterSource.getValues().toString());

    return historicalTransactionManager.findFromHistory(
        sql.toString(), mapSqlParameterSource, newRowMapper());
  }

  public E cancel(
      Symbol symbol,
      Long userId,
      Long id,
      OrderOperator orderOperator,
      CurrencyPairConfig currencyPairConfig,
      Currency baseCurrency,
      Currency quoteCurrency,
      CancelReason cancelReason,
      EntityManager entityManager)
      throws Exception {

    log.info("[tid={}] ordertradelog,symbolId={},cancel_transaction_start,orderId={}", TraceIdHolder.getTraceIdOrUnknown(), symbol.getId(), id);

    E spotOrder = findOne(id, entityManager);

    Asset baseAsset =
        assetService.findOrCreate(
            userId, symbol.getCurrencyPair().getBaseCurrency(), entityManager);
    Asset quoteAsset =
        assetService.findOrCreate(
            userId, symbol.getCurrencyPair().getQuoteCurrency(), entityManager);

    if (baseAsset == null || quoteAsset == null) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_ASSET_NOT_FOUND,
          "userId: "
              + JsonUtil.encode(userId)
              + ", symbol: "
              + JsonUtil.encode(symbol)
              + ", baseAsset: "
              + JsonUtil.encode(baseAsset)
              + ", quoteAsset: "
              + JsonUtil.encode(quoteAsset));
    }

    if (spotOrder == null) {
      log.warn("[tid={}] ORDER ERROR! ORDER NOT FOUND: cancel orderId={},userId={}", TraceIdHolder.getTraceIdOrUnknown(), id, userId);
      throw new CustomException(
          ErrorCode.ORDER_ERROR_ORDER_NOT_FOUND,
          "cancel orderId: " + JsonUtil.encode(id) + ", userId: " + JsonUtil.encode(userId));
    }

    if (!userId.equals(spotOrder.getUserId())) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_INVALID_BROKER,
          "cancel userId: "
              + JsonUtil.encode(userId)
              + " ,spotOrder_userId: "
              + JsonUtil.encode(spotOrder.getUserId())
              + " ,id: "
              + JsonUtil.encode(id));
    }

    // 取引可否チェック
    // ADMIN, WORKER:常にキャンセル可能
    // isTradable=false：発注不可、キャンセル可能
    if (orderOperator == OrderOperator.USER) {

      // 通貨ペア設定
      if (!currencyPairConfig.isEnabled()) {
        throw new CustomException(
            ErrorCode.ORDER_ERROR_ORDER_IS_INACTIVE,
            "cancel orderId: "
                + id
                + ", userId: "
                + userId
                + ", tradable: "
                + currencyPairConfig.isTradable()
                + ", enabled: "
                + currencyPairConfig.isEnabled()
                + ", orderOperator: "
                + orderOperator);
      }

      // ユーザー
      User user = userService.findOne(userId, entityManager);

      if (user == null) {
        throw new CustomException(
            ErrorCode.REQUEST_ERROR_USER_NOT_FOUND,
            "cancel orderId: " + id + ", userId: " + userId);
      }

      if (!user.isAllowedToLogin()) {
        throw new CustomException(
            ErrorCode.REQUEST_ERROR_INVALID_USER_STATUS, "userId," + user.getId());
      }
    }

    // orderOperator, orderStatusのセット
    spotOrder.cancel(orderOperator);
    spotOrder.setCancelReason(cancelReason);

    // 発注総額の再現(発注時価格×発注数量(残数量) )
    // 成行：発注時平均価格×発注数量(残数量)、指値：発注価格×発注数量(残数量)
    // ロック資産は桁数処理を行わない（入出金対象外であり、かつ部分約定時に端数が残るのを回避するため)
    BigDecimal assetAmountOrder = spotOrder.getPrice().multiply(spotOrder.getRemainingAmount());

    // 資産ロック解除額チェック:valid
    // ロック資産は桁数処理を行わない（入出金対象外であり、かつ部分約定時に端数が残るのを回避するため)
    BigDecimal unlockAmountOrder = spotOrder.getRemainingAmount().negate();
    BigDecimal unlockAssetAmountOrder = assetAmountOrder.negate();

    if (unlockAmountOrder == null || unlockAmountOrder.signum() > -1) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_INVALID_AMOUNT,
          "lockAmountOrder: "
              + JsonUtil.encode(unlockAmountOrder)
              + ", userId: "
              + JsonUtil.encode(userId)
              + ", spotOrder: "
              + JsonUtil.encode(spotOrder));
    }

    if (unlockAssetAmountOrder == null || unlockAssetAmountOrder.signum() > -1) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_INVALID_AMOUNT,
          "unlockAssetAmountOrder: "
              + JsonUtil.encode(unlockAssetAmountOrder)
              + ", userId: "
              + JsonUtil.encode(userId)
              + ", spotOrder: "
              + JsonUtil.encode(spotOrder));
    }

    // 資産ロック解除
    if (spotOrder.getOrderSide().isSell()) {

      // 売り
      // ・base資産：変更なし
      // ・baseロック資産：残注文数量を減算
      // ・quote資産：変更なし
      // ・quoteロック資産：変更なし
      if (baseAsset.getLockedAmount().add(unlockAmountOrder).signum() < 0) {

        throw new CustomException(
            ErrorCode.ORDER_ERROR_INVALID_ASSET,
            "lockedAmount: "
                + JsonUtil.encode(baseAsset.getLockedAmount())
                + ", unlockAmountOrder: "
                + JsonUtil.encode(unlockAmountOrder)
                + ", spotOrder_remainingAmount: "
                + JsonUtil.encode(spotOrder.getRemainingAmount())
                + ", spotOrder_amount: "
                + JsonUtil.encode(spotOrder.getAmount())
                + ", userId: "
                + JsonUtil.encode(userId)
                + ", spotOrder: "
                + JsonUtil.encode(spotOrder));
      }

      // ・base資産：変更なし
      // ・baseロック資産：残注文数量を減算
      assetService.updateWithExternalLock(
          spotOrder.getUserId(),
          symbol.getCurrencyPair().getBaseCurrency(),
          BigDecimal.ZERO,
          unlockAmountOrder,
          entityManager);

    } else {
      // 買い
      // ・base資産：変更なし
      // ・baseロック資産：変更なし
      // ・quote資産：変更なし
      // ・quoteロック資産：注文総額を残注文数量の分減算
      if (quoteAsset.getLockedAmount().add(unlockAssetAmountOrder).signum() < 0) {

        throw new CustomException(
            ErrorCode.ORDER_ERROR_INVALID_ASSET,
            "lockedAmount: "
                + JsonUtil.encode(quoteAsset.getLockedAmount())
                + ", unlockAssetAmountOrder: "
                + JsonUtil.encode(unlockAssetAmountOrder)
                + ", assetAmountOrder: "
                + JsonUtil.encode(assetAmountOrder)
                + ", spotOrder_Price: "
                + JsonUtil.encode(spotOrder.getPrice())
                + ", spotOrder_RemainingAmount: "
                + JsonUtil.encode(spotOrder.getRemainingAmount())
                + ", spotOrder_amount: "
                + JsonUtil.encode(spotOrder.getAmount())
                + ", userId: "
                + JsonUtil.encode(userId)
                + ", spotOrder: "
                + JsonUtil.encode(spotOrder));
      }

      // ・quote資産：変更なし
      // ・quoteロック資産：注文総額を残注文数量の分減算
      assetService.updateWithExternalLock(
          spotOrder.getUserId(),
          symbol.getCurrencyPair().getQuoteCurrency(),
          BigDecimal.ZERO,
          unlockAssetAmountOrder,
          entityManager);
    }

    // asset更新後チェック：残資産マイナスでないこと
    if (baseAsset.getOnhandAmount().subtract(baseAsset.getLockedAmount()).signum() < 0
        || quoteAsset.getOnhandAmount().subtract(quoteAsset.getLockedAmount()).signum() < 0
        || baseAsset.getOnhandAmount().signum() < 0
        || baseAsset.getLockedAmount().signum() < 0
        || quoteAsset.getOnhandAmount().signum() < 0
        || quoteAsset.getLockedAmount().signum() < 0) {

      throw new CustomException(
          ErrorCode.ORDER_ERROR_INVALID_ASSET,
          "base_onhandAmount: "
              + JsonUtil.encode(baseAsset.getOnhandAmount())
              + ", base_lockedAmount: "
              + JsonUtil.encode(baseAsset.getLockedAmount())
              + ", quote_onhandAmount: "
              + JsonUtil.encode(quoteAsset.getOnhandAmount())
              + ", quote_lockedAmount: "
              + JsonUtil.encode(quoteAsset.getLockedAmount())
              + ", userId: "
              + JsonUtil.encode(userId)
              + ", spotOrder: "
              + JsonUtil.encode(spotOrder));
    }

    save(spotOrder, entityManager);

    log.info("ordertradelog,symbolId," + symbol.getId() + ",cancel_transaction_end,orderId," + id);

    if (cancelReason == CancelReason.CUSTOMER_CANCEL) {
      sendCustomerCancelMail(spotOrder, mailNoreplyService, sesManager, symbol, userService.findOne(userId));
    } else {
      sendOtherCancelMail(spotOrder, mailNoreplyService, sesManager, symbol, userService.findOne(userId));
    }

    return spotOrder;
  }

  @SuppressWarnings("unchecked")
  public E cancel(Symbol symbol, Long userId, Long id, OrderOperator orderOperator, CancelReason cancelReason)
      throws Exception {

    log.info("ordertradelog,symbolId," + symbol.getId() + ",cancel_start,orderId," + id);

    CurrencyPairConfig currencyPairConfig =
        currencyPairConfigService.findByCondition(symbol.getTradeType(), symbol.getCurrencyPair());

    if (currencyPairConfig == null) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_CURRENCY_PAIR_CONFIG_NOT_FOUND,
          "currencyPair: " + JsonUtil.encode(symbol.getCurrencyPair()));
    }

    Currency baseCurrency = symbol.getCurrencyPair().getBaseCurrency();
    Currency quoteCurrency = symbol.getCurrencyPair().getQuoteCurrency();

    CurrencyConfig baseCurrencyConfig = currencyConfigService.findByCurrency(baseCurrency);
    CurrencyConfig quoteCurrencyConfig = currencyConfigService.findByCurrency(quoteCurrency);

    if (baseCurrencyConfig == null || quoteCurrencyConfig == null) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_CURRENCY_CONFIG_NOT_FOUND, "symbol: " + JsonUtil.encode(symbol));
    }

    // 個別注文処理のlock取得失敗時、or lock取得後のexecute時のエラーはexceptionで返す
    // execute時のエラー、トランザクションエラー、DB saveエラーは内部処理でexceptionを返す
    // lock取得失敗時のみ本処理（親処理）でexceptionを出す(return==nullはexception)

    // 約定処理内からcancelされるケースを考慮し、lockParamsは約定処理と同じとする
    return (E)
        redisManager.executeWithLock(
            getLockKey(userId, baseCurrency),
            LockParams.EXECUTE_ORDER,
            () -> {
              return (E)
                  redisManager.executeWithLock(
                      getLockKey(userId, quoteCurrency),
                      LockParams.EXECUTE_ORDER,
                      () -> {
                        return (E)
                            redisManager.executeWithLock(
                                Order.getLockKey(symbol, id),
                                LockParams.EXECUTE_ORDER,
                                () -> {
                                  return (E)
                                      customTransactionManager.execute(
                                          entityManager -> {
                                            // 【注意】トランザクション内ではentityManager引数無しのクエリを使用しないこと(save,find等。findOneはentityManager引数必須)
                                            // トランザクション内では、エラー時roll-backさせるためexceptionを出す(save,cancel処理含む)。try-catch使用注意
                                            return cancel(
                                                symbol,
                                                userId,
                                                id,
                                                orderOperator,
                                                currencyPairConfig,
                                                baseCurrency,
                                                quoteCurrency,
                                                cancelReason,
                                                entityManager);
                                          });
                                });
                      });
            });
  }

  @SuppressWarnings("unchecked")
  public E marketOrder(Symbol symbol, User user, SpotOrderForm form, OrderChannel orderChannel)
      throws Exception {

    log.info("ordertradelog,symbolId," + symbol.getId() + ",marketorder_start");

    CurrencyPairConfig currencyPairConfig =
        currencyPairConfigService.findByCondition(symbol.getTradeType(), symbol.getCurrencyPair());

    if (!user.isTradable()) {
      throw new CustomException(
          ErrorCode.REQUEST_ERROR_INVALID_USER_STATUS, "userId," + user.getId());
    }

    if (user.getLevel() < 2) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_SECURITY_LEVEL,
          "userId," + user.getId() + ",securityLevel," + user.getLevel());
    }

    if (currencyPairConfig == null) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_CURRENCY_PAIR_CONFIG_NOT_FOUND,
          "currencyPair: " + JsonUtil.encode(symbol.getCurrencyPair()));
    }

    Currency baseCurrency = symbol.getCurrencyPair().getBaseCurrency();
    Currency quoteCurrency = symbol.getCurrencyPair().getQuoteCurrency();

    CurrencyConfig baseCurrencyConfig = currencyConfigService.findByCurrency(baseCurrency);
    CurrencyConfig quoteCurrencyConfig = currencyConfigService.findByCurrency(quoteCurrency);

    if (baseCurrencyConfig == null || quoteCurrencyConfig == null) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_CURRENCY_CONFIG_NOT_FOUND,
          "userId: " + JsonUtil.encode(user.getId()) + ", form: " + JsonUtil.encode(form));
    }

    OrderSide orderSide = OrderSide.valueOf(form.getOrderSide());
    OrderType orderType = OrderType.valueOf(form.getOrderType());

    // 取引可否チェック
    if (!currencyPairConfig.isTradable() || !currencyPairConfig.isEnabled()) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_ORDER_IS_INACTIVE,
          "userId: "
              + JsonUtil.encode(user.getId())
              + ", tradable: "
              + JsonUtil.encode(currencyPairConfig.isTradable())
              + ", enabled: "
              + JsonUtil.encode(currencyPairConfig.isEnabled())
              + ", form: "
              + JsonUtil.encode(form));
    }

    BigDecimal amount = form.getAmount();

    // 発注数量チェック:valid
    if (amount == null || amount.signum() < 1) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_INVALID_AMOUNT,
          "userId: "
              + JsonUtil.encode(user.getId())
              + ", amount: "
              + JsonUtil.encode(amount)
              + ", form: "
              + JsonUtil.encode(form));
    }

    // 発注数量が指定桁数以内であること
    // 例：1234.12300001:8桁, 1234.12300000:3桁、1234.123:3桁
    if (amount.scale() > symbol.getCurrencyPair().getBasePrecision()) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_INVALID_AMOUNT,
          "userId: "
              + JsonUtil.encode(user.getId())
              + ", amount: "
              + JsonUtil.encode(amount)
              + ", amount_scale: "
              + JsonUtil.encode(amount.scale())
              + ", basePrecision: "
              + JsonUtil.encode(symbol.getCurrencyPair().getBasePrecision())
              + ", form: "
              + JsonUtil.encode(form));
    }

    // 発注数量算出：桁数処理（通貨ペア単位）
    BigDecimal amountScaled =
        currencyPairConfig.getCurrencyPair().getScaledAmount(amount, RoundingMode.FLOOR);

    // 発注数量チェック:valid
    if (amountScaled == null || amountScaled.signum() < 1) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_INVALID_AMOUNT,
          "userId: "
              + JsonUtil.encode(user.getId())
              + ", amount: "
              + JsonUtil.encode(amount)
              + ", amountScaled: "
              + JsonUtil.encode(amountScaled)
              + ", form: "
              + JsonUtil.encode(form));
    }

    // 1注文の最小・最大注文数量チェック
    // 上限撤廃フラグ(apiより)が立っているときはスキップ
    if (!user.isTradeUncapped()
        && (amountScaled.compareTo(currencyPairConfig.getMaxOrderAmount()) > 0
            || amountScaled.compareTo(currencyPairConfig.getMinOrderAmount()) < 0)) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_AMOUNT_OUT_OF_MINMAX,
          "userId: "
              + JsonUtil.encode(user.getId())
              + ", amount: "
              + JsonUtil.encode(amount)
              + ", amountScaled: "
              + JsonUtil.encode(amountScaled)
              + ", amountScaled: "
              + JsonUtil.encode(amountScaled)
              + ", maxOrderAmount: "
              + JsonUtil.encode(currencyPairConfig.getMaxOrderAmount())
              + ", minOrderAmount: "
              + JsonUtil.encode(currencyPairConfig.getMinOrderAmount())
              + ", form: "
              + JsonUtil.encode(form));
    }

    // 発注数量チェック：ベストプライスからの指定範囲の合計数量以内
    OrderSide makerOrderSide = orderSide.getOpposite();
    BigDecimal makerBestPrice = getBestPrice(symbol.getId(), orderSide.getOpposite());

    // 板枯渇時はエラー（成行は価格が取れないためエラー)
    if (makerBestPrice == null || makerBestPrice.signum() < 1) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_SPOT_ORDER_NOT_FOUND,
          "makerBestPrice: "
              + JsonUtil.encode(makerBestPrice)
              + ", userId: "
              + JsonUtil.encode(user.getId())
              + ", form: "
              + JsonUtil.encode(form));
    }

    BigDecimal marketPriceRange =
        makerBestPrice.multiply(currencyPairConfig.getMarketPriceRangeRate());
    BigDecimal amountSum = BigDecimal.ZERO;

    for (E limitSpotOrder :
        makerOrderSide.isSell()
            ? findByCondition(
                symbol.getId(),
                makerOrderSide,
                makerBestPrice,
                makerBestPrice.add(marketPriceRange))
            : findByCondition(
                symbol.getId(),
                makerOrderSide,
                makerBestPrice.subtract(marketPriceRange),
                makerBestPrice)) {
      amountSum = amountSum.add(limitSpotOrder.getRemainingAmount());
    }

    if (amountSum.signum() < 1
        || amountScaled.compareTo(amountSum.multiply(currencyPairConfig.getMarketAmountRangeRate()))
            > 0) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_AMOUNT_OUT_OF_RANGE,
          "userId: "
              + JsonUtil.encode(user.getId())
              + ", amount: "
              + JsonUtil.encode(amount)
              + ", amountScaled: "
              + JsonUtil.encode(amountScaled)
              + ", amountSum: "
              + JsonUtil.encode(amountSum)
              + ", MarketAmountRangeRate: "
              + JsonUtil.encode(currencyPairConfig.getMarketAmountRangeRate())
              + ", makerBestPrice: "
              + JsonUtil.encode(makerBestPrice)
              + ", marketPriceRange: "
              + JsonUtil.encode(marketPriceRange));
    }

    // 発注総額概算算出 ※利用可能額チェック用
    // 反対サイドベストプライス×発注数量(通貨ペア単位桁数処理後)
    BigDecimal assetAmountBest = makerBestPrice.multiply(amountScaled);

    if (assetAmountBest == null || assetAmountBest.signum() < 1) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_INVALID_AMOUNT,
          "userId: "
              + JsonUtil.encode(user.getId())
              + ", assetAmountBest: "
              + JsonUtil.encode(assetAmountBest)
              + ", makerBestPrice: "
              + JsonUtil.encode(makerBestPrice)
              + ", amountScaled: "
              + JsonUtil.encode(amountScaled)
              + ", amount: "
              + JsonUtil.encode(amount)
              + ", form: "
              + JsonUtil.encode(form));
    }

    // 手数料概算算出 ※利用可能額チェック用
    // 発注総額概算(ベストプライス)より
    BigDecimal fee =
        SpotTradeService.getBean(symbol)
            .calculateFee(symbol, currencyPairConfig, assetAmountBest, TradeAction.TAKER);
    if (fee == null) {
      throw new CustomException(ErrorCode.ORDER_ERROR_INVALID_FEE, "fee: " + JsonUtil.encode(fee));
    }

    // 平均価格算出(桁数処理後・通貨ペア単位)
    // ※発注数量の範囲で平均価格を算出(板全体を見ると大きすぎる金額となるケースがあるため)
    BigDecimal orderPriceAverage =
        orderSide.isSell()
            ? currencyPairConfig
                .getCurrencyPair()
                .getScaledPrice(
                    orderBookService
                        .get(symbol)
                        .getAverageBid(
                            symbol, currencyPairConfig, amountScaled, orderType, orderSide),
                    RoundingMode.FLOOR)
            : currencyPairConfig
                .getCurrencyPair()
                .getScaledPrice(
                    orderBookService
                        .get(symbol)
                        .getAverageAsk(
                            symbol, currencyPairConfig, amountScaled, orderType, orderSide),
                    RoundingMode.FLOOR);

    // 板枯渇時
    if (orderPriceAverage == null || orderPriceAverage.signum() < 1) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_ORDERBOOK_NOT_FOUND,
          "orderPriceAverage: "
              + JsonUtil.encode(orderPriceAverage)
              + ", userId: "
              + JsonUtil.encode(user.getId())
              + ", form: "
              + JsonUtil.encode(form));
    }

    // 個別注文処理のlock取得失敗時、or lock取得後のexecute時のエラーはexceptionで返す
    // execute時のエラー、トランザクションエラー、DB saveエラーは内部処理でexceptionを返す
    // lock取得失敗時のみ本処理（親処理）でexceptionを出す(return==nullはexception)
    return (E)
        redisManager.executeWithLock(
            getLockKey(user.getId(), baseCurrency),
            LockParams.ORDER,
            () -> {
              return (E)
                  redisManager.executeWithLock(
                      getLockKey(user.getId(), quoteCurrency),
                      LockParams.ORDER,
                      () -> {
                        return (E)
                            customTransactionManager.execute(
                                entityManager -> {
                                  log.info(
                                      "ordertradelog,symbolId,"
                                          + symbol.getId()
                                          + ",marketorder_transaction_start");

                                  // 【注意】トランザクション内ではentityManager引数無しのクエリを使用しないこと(save,find等。findOneはentityManager引数必須)
                                  // トランザクション内では、エラー時roll-backさせるためexceptionを出す(save,cancel処理含む)。try-catch使用注意
                                  Asset quoteAsset =
                                      assetService.findOrCreate(
                                          user.getId(), quoteCurrency, entityManager);
                                  Asset baseAsset =
                                      assetService.findOrCreate(
                                          user.getId(), baseCurrency, entityManager);

                                  if (baseAsset == null || quoteAsset == null) {
                                    throw new CustomException(
                                        ErrorCode.ORDER_ERROR_ASSET_NOT_FOUND,
                                        "userId: "
                                            + JsonUtil.encode(user.getId())
                                            + ", symbol: "
                                            + JsonUtil.encode(symbol)
                                            + ", baseAsset: "
                                            + JsonUtil.encode(baseAsset)
                                            + ", quoteAsset: "
                                            + JsonUtil.encode(quoteAsset));
                                  }

                                  // 発注総額概算算出
                                  // ロック資産算出用の平均価格での概算算出（キャンセル時再現用の桁数処理）
                                  // ロック資産は桁数処理を行わない（入出金対象外であり、かつ部分約定時に端数が残るのを回避するため)
                                  BigDecimal assetAmountOrder =
                                      orderPriceAverage.multiply(amountScaled);

                                  // 発注総額概算チェック:valid
                                  if (assetAmountOrder == null || assetAmountOrder.signum() < 1) {
                                    throw new CustomException(
                                        ErrorCode.ORDER_ERROR_INVALID_AMOUNT,
                                        "assetAmountOrder: "
                                            + JsonUtil.encode(assetAmountOrder)
                                            + ", userId: "
                                            + JsonUtil.encode(user.getId())
                                            + ", form: "
                                            + JsonUtil.encode(form));
                                  }

                                  // 利用可能額チェック
                                  if (orderSide.isSell()) {
                                    // ロック資産は桁数処理を行わない（入出金対象外であり、かつ部分約定時に端数が残るのを回避するため)

                                    if (baseAsset
                                            .getOnhandAmount()
                                            .subtract(baseAsset.getLockedAmount())
                                            .compareTo(amountScaled)
                                        < 0) {
                                      throw new CustomException(
                                          ErrorCode.ORDER_ERROR_AMOUNT_EXCEED_ASSET,
                                          "base_onhandAmount: "
                                              + JsonUtil.encode(baseAsset.getOnhandAmount())
                                              + ", base_lockedAmount: "
                                              + JsonUtil.encode(baseAsset.getLockedAmount())
                                              + ", base_remainingAmount: "
                                              + JsonUtil.encode(
                                                  baseAsset
                                                      .getOnhandAmount()
                                                      .subtract(baseAsset.getLockedAmount()))
                                              + ", amountScaled: "
                                              + JsonUtil.encode(amountScaled)
                                              + ", userId: "
                                              + JsonUtil.encode(user.getId())
                                              + ", form: "
                                              + JsonUtil.encode(form));
                                    }

                                  } else {

                                    if (quoteAsset
                                            .getOnhandAmount()
                                            .subtract(quoteAsset.getLockedAmount())
                                            .compareTo(assetAmountOrder)
                                        < 0) {
                                      throw new CustomException(
                                          ErrorCode.ORDER_ERROR_AMOUNT_EXCEED_ASSET,
                                          "quote_onhandAmount: "
                                              + JsonUtil.encode(quoteAsset.getOnhandAmount())
                                              + ", quote_lockedAmount: "
                                              + JsonUtil.encode(quoteAsset.getLockedAmount())
                                              + ", quote_remainingAmount: "
                                              + JsonUtil.encode(
                                                  quoteAsset
                                                      .getOnhandAmount()
                                                      .subtract(quoteAsset.getLockedAmount()))
                                              + ", assetAmountBest: "
                                              + JsonUtil.encode(assetAmountBest)
                                              + ", assetAmountOrder: "
                                              + JsonUtil.encode(assetAmountOrder)
                                              + ", userId: "
                                              + JsonUtil.encode(user.getId())
                                              + ", form: "
                                              + JsonUtil.encode(form));
                                    }
                                  }

                                  // 1日の取引上限チェック：通貨単位
                                  // 上限撤廃フラグが立っているときはスキップ
                                  if (!user.isTradeUncapped()) {
                                    BigDecimal baseAmountSumDay =
                                        getOrderTradeAmountPerDay(
                                            amountScaled,
                                            baseCurrencyConfig,
                                            baseCurrency,
                                            user,
                                            entityManager);

                                    if (baseAmountSumDay.compareTo(
                                            baseCurrencyConfig.getMaxOrderAmountPerDay())
                                        > 0) {

                                      throw new CustomException(
                                          ErrorCode.ORDER_ERROR_AMOUNT_OVER_MAX_PER_DAY,
                                          "userId: "
                                              + JsonUtil.encode(user.getId())
                                              + ", form: "
                                              + JsonUtil.encode(form));
                                    }

                                    BigDecimal quoteAmountSumDay =
                                        getOrderTradeAmountPerDay(
                                            assetAmountBest,
                                            quoteCurrencyConfig,
                                            quoteCurrency,
                                            user,
                                            entityManager);

                                    if (quoteAmountSumDay.compareTo(
                                            quoteCurrencyConfig.getMaxOrderAmountPerDay())
                                        > 0) {

                                      throw new CustomException(
                                          ErrorCode.ORDER_ERROR_AMOUNT_OVER_MAX_PER_DAY,
                                          "userId: "
                                              + JsonUtil.encode(user.getId())
                                              + ", form: "
                                              + JsonUtil.encode(form));
                                    }
                                  }

                                  // アクティブ注文数量の上限チェック：通貨単位
                                  // 上限撤廃フラグ(apiより)が立っているときはスキップ
                                  if (!user.isTradeUncapped()) {
                                    checkMaxActiveOrderAmount(form, user.getId(), symbol, currencyPairConfig, entityManager);
                                  }

                                  // 資産ロック
                                  if (orderSide.isSell()) {
                                    // Base:注文数量でロック（通貨ペア単位の桁数処理後、通貨単位の桁数処理なし)
                                    assetService.updateWithExternalLock(
                                        user.getId(),
                                        symbol.getCurrencyPair().getBaseCurrency(),
                                        BigDecimal.ZERO,
                                        amountScaled,
                                        entityManager);

                                  } else {
                                    // Quote:注文数量×板平均価格で算出した注文総額概算でロック(通貨ペア単位の桁数処理後、通貨単位の桁数処理なし)
                                    assetService.updateWithExternalLock(
                                        user.getId(),
                                        symbol.getCurrencyPair().getQuoteCurrency(),
                                        BigDecimal.ZERO,
                                        assetAmountOrder,
                                        entityManager);
                                  }

                                  // asset更新後チェック：残資産マイナスでないこと
                                  if (baseAsset
                                              .getOnhandAmount()
                                              .subtract(baseAsset.getLockedAmount())
                                              .signum()
                                          < 0
                                      || quoteAsset
                                              .getOnhandAmount()
                                              .subtract(quoteAsset.getLockedAmount())
                                              .signum()
                                          < 0
                                      || baseAsset.getOnhandAmount().signum() < 0
                                      || baseAsset.getLockedAmount().signum() < 0
                                      || quoteAsset.getOnhandAmount().signum() < 0
                                      || quoteAsset.getLockedAmount().signum() < 0) {

                                    throw new CustomException(
                                        ErrorCode.ORDER_ERROR_INVALID_ASSET,
                                        "baseOnhandAmount: "
                                            + JsonUtil.encode(baseAsset.getOnhandAmount())
                                            + ", baseLockedAmount: "
                                            + JsonUtil.encode(baseAsset.getLockedAmount())
                                            + ", quoteOnhandAmount: "
                                            + JsonUtil.encode(quoteAsset.getOnhandAmount())
                                            + ", quoteLockedAmount: "
                                            + JsonUtil.encode(quoteAsset.getLockedAmount())
                                            + ", userId: "
                                            + JsonUtil.encode(user.getId())
                                            + ", form: "
                                            + JsonUtil.encode(form));
                                  }

                                  E spotOrder = newEntity();
                                  spotOrder.setProperties(
                                      symbol.getId(), user.getId(), amountScaled);

                                  // PostOnly機能オフ(false固定)
                                  // spotOrder.setAnotherProperties(orderSide, orderType,
                                  // orderChannel,
                                  // orderPriceAverage, amountScaled, OrderStatus.UNFILLED,
                                  // OrderOperator.USER,
                                  // form.isPostOnly());
                                  spotOrder.setAnotherProperties(
                                      orderSide,
                                      orderType,
                                      orderChannel,
                                      orderPriceAverage,
                                      amountScaled,
                                      OrderStatus.UNFILLED,
                                      OrderOperator.USER,
                                      false);

                                  return save(spotOrder, entityManager);
                                });
                      });
            });
  }

  @SuppressWarnings("unchecked")
  public E limitStopOrder(Symbol symbol, User user, SpotOrderForm form, OrderChannel orderChannel)
      throws Exception {

    log.info("ordertradelog,symbolId," + symbol.getId() + ",limitorder_start");

    if (!user.isTradable()) {
      throw new CustomException(
          ErrorCode.REQUEST_ERROR_INVALID_USER_STATUS, "userId," + user.getId());
    }

    if (user.getLevel() < 2) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_SECURITY_LEVEL,
          "userId," + user.getId() + ",securityLevel," + user.getLevel());
    }

    CurrencyPairConfig currencyPairConfig =
        currencyPairConfigService.findByCondition(symbol.getTradeType(), symbol.getCurrencyPair());

    if (currencyPairConfig == null) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_CURRENCY_PAIR_CONFIG_NOT_FOUND,
          "currencyPair: " + JsonUtil.encode(symbol.getCurrencyPair()));
    }

    Currency baseCurrency = symbol.getCurrencyPair().getBaseCurrency();
    Currency quoteCurrency = symbol.getCurrencyPair().getQuoteCurrency();

    CurrencyConfig baseCurrencyConfig = currencyConfigService.findByCurrency(baseCurrency);
    CurrencyConfig quoteCurrencyConfig = currencyConfigService.findByCurrency(quoteCurrency);

    if (baseCurrencyConfig == null || quoteCurrencyConfig == null) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_CURRENCY_CONFIG_NOT_FOUND,
          "userId: " + JsonUtil.encode(user.getId()) + ", form: " + JsonUtil.encode(form));
    }

    // 取引可否チェック
    if (!currencyPairConfig.isTradable() || !currencyPairConfig.isEnabled()) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_ORDER_IS_INACTIVE,
          "userId: "
              + JsonUtil.encode(user.getId())
              + ", tradable: "
              + JsonUtil.encode(currencyPairConfig.isTradable())
              + ", enabled: "
              + JsonUtil.encode(currencyPairConfig.isEnabled())
              + ", form: "
              + JsonUtil.encode(form));
    }

    OrderSide orderSide = OrderSide.valueOf(form.getOrderSide());
    OrderType orderType = OrderType.valueOf(form.getOrderType());

    BigDecimal price = form.getPrice();
    BigDecimal amount = form.getAmount();

    // 発注価格チェック
    if (price == null) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_PRICE_NOT_FOUND,
          "userId: "
              + JsonUtil.encode(user.getId())
              + ", price: "
              + JsonUtil.encode(price)
              + ", form: "
              + JsonUtil.encode(form));
    }

    if (price.signum() < 1) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_PRICE_OUT_OF_RANGE,
          "userId: "
              + JsonUtil.encode(user.getId())
              + ", price: "
              + JsonUtil.encode(price)
              + ", form: "
              + JsonUtil.encode(form));
    }

    // 発注価格が指定桁数以内であること
    // 例：1234.12300001:8桁, 1234.12300000:3桁、1234.123:3桁
    if (price.scale() > symbol.getCurrencyPair().getQuotePrecision()) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_INVALID_PRICE,
          "userId: "
              + JsonUtil.encode(user.getId())
              + ", price: "
              + JsonUtil.encode(price)
              + ", price_scale: "
              + JsonUtil.encode(price.scale())
              + ", quotePrecision: "
              + JsonUtil.encode(symbol.getCurrencyPair().getQuotePrecision())
              + ", form: "
              + JsonUtil.encode(form));
    }

    // 注文価格チェック(通貨ぺア単位桁数処理後):valid
    BigDecimal priceScaled = currencyPairConfig.getCurrencyPair().getScaledPrice(price);

    if (priceScaled == null || priceScaled.signum() < 1) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_PRICE_OUT_OF_RANGE,
          "priceScaled: "
              + JsonUtil.encode(priceScaled)
              + ", userId: "
              + JsonUtil.encode(user.getId())
              + ", form: "
              + JsonUtil.encode(form));
    }

    // 発注数量チェック:valid
    if (amount == null || amount.signum() < 1) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_INVALID_AMOUNT,
          "userId: "
              + JsonUtil.encode(user.getId())
              + ", amount: "
              + JsonUtil.encode(amount)
              + ", form: "
              + JsonUtil.encode(form));
    }

    // 発注数量が指定桁数以内であること
    // 例：1234.12300001:8桁, 1234.12300000:3桁、1234.123:3桁
    if (amount.scale() > symbol.getCurrencyPair().getBasePrecision()) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_INVALID_AMOUNT,
          "userId: "
              + JsonUtil.encode(user.getId())
              + ", amount: "
              + JsonUtil.encode(amount)
              + ", amount_scale: "
              + JsonUtil.encode(amount.scale())
              + ", basePrecision: "
              + JsonUtil.encode(symbol.getCurrencyPair().getBasePrecision())
              + ", form: "
              + JsonUtil.encode(form));
    }

    // 発注数量算出（通貨ペア桁数処理後）
    BigDecimal amountScaled =
        currencyPairConfig.getCurrencyPair().getScaledAmount(amount, RoundingMode.FLOOR);

    if (amountScaled == null || amountScaled.signum() < 1) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_INVALID_AMOUNT,
          "userId: "
              + JsonUtil.encode(user.getId())
              + ", amount: "
              + JsonUtil.encode(amount)
              + ", amountScaled: "
              + JsonUtil.encode(amountScaled)
              + ", form: "
              + JsonUtil.encode(form));
    }

    // 1注文の最小・最大注文数量チェック
    // 上限撤廃フラグ(apiより)が立っているときはスキップ
    if (!user.isTradeUncapped()
        && (amountScaled.compareTo(currencyPairConfig.getMaxOrderAmount()) > 0
            || amountScaled.compareTo(currencyPairConfig.getMinOrderAmount()) < 0)) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_AMOUNT_OUT_OF_MINMAX,
          "userId: "
              + JsonUtil.encode(user.getId())
              + ", amount: "
              + JsonUtil.encode(amount)
              + ", amountScaled: "
              + JsonUtil.encode(amountScaled)
              + ", maxOrderAmount: "
              + JsonUtil.encode(currencyPairConfig.getMaxOrderAmount())
              + ", minOrderAmount: "
              + JsonUtil.encode(currencyPairConfig.getMinOrderAmount())
              + ", form: "
              + JsonUtil.encode(form));
    }

    // 発注可能価格・数量範囲チェック用
    OrderSide makerOrderSide = orderSide.getOpposite();
    SpotOrder makerBestOrder = findBestSpotOrder(symbol.getId(), makerOrderSide);

    // 板枯渇時、指値はエラーとせずチェック回避(marketmaker想定)
    if (makerBestOrder == null) {
      log.info(
          "makerBestPrice not found"
              + JsonUtil.encode(amount)
              + ", userId: "
              + JsonUtil.encode(user.getId())
              + ", form: "
              + JsonUtil.encode(form));
    }

    // 発注可能数量範囲チェック
    if (orderType == OrderType.STOP) {
      // 発注数量チェック：ベストプライスからの指定範囲の合計数量以内
      BigDecimal makerBestPrice = makerBestOrder.getPrice();

      // 板枯渇時はエラー（成行は価格が取れないためエラー)
      if (makerBestPrice == null || makerBestPrice.signum() < 1) {
        throw new CustomException(
            ErrorCode.ORDER_ERROR_SPOT_ORDER_NOT_FOUND,
            "makerBestPrice,"
                + makerBestPrice
                + ",userId,"
                + user.getId()
                + ",form,"
                + JsonUtil.encode(form));
      }

      BigDecimal marketPriceRange =
          makerBestPrice.multiply(currencyPairConfig.getMarketPriceRangeRate());
      BigDecimal amountSum = BigDecimal.ZERO;

      for (E limitSpotOrder :
          makerOrderSide.isSell()
              ? findByCondition(
                  symbol.getId(),
                  makerOrderSide,
                  makerBestPrice,
                  makerBestPrice.add(marketPriceRange))
              : findByCondition(
                  symbol.getId(),
                  makerOrderSide,
                  makerBestPrice.subtract(marketPriceRange),
                  makerBestPrice)) {
        amountSum = amountSum.add(limitSpotOrder.getRemainingAmount());
      }

      if (amountSum.signum() < 1
          || amountScaled.compareTo(
                  amountSum.multiply(currencyPairConfig.getMarketAmountRangeRate()))
              > 0) {
        throw new CustomException(
            ErrorCode.ORDER_ERROR_AMOUNT_OUT_OF_RANGE,
            "userId,"
                + user.getId()
                + ",amount,"
                + amount
                + ",amountScaled,"
                + amountScaled
                + ",amountSum,"
                + amountSum
                + ",MarketAmountRangeRate,"
                + currencyPairConfig.getMarketAmountRangeRate()
                + ",makerBestPrice,"
                + makerBestPrice
                + ",marketPriceRange,"
                + marketPriceRange);
      }
    }

    // 発注可能価格範囲チェック
    if (makerBestOrder != null) {
      if (orderType == OrderType.LIMIT) {
        BigDecimal limitPriceRange =
            makerBestOrder.getPrice().multiply(currencyPairConfig.getLimitPriceRangeRate());
        BigDecimal limitPriceRangeMax =
            makerBestOrder.getPrice().multiply(new BigDecimal("0.5"));
        // 指値注文：不利な価格の発注はOKだが、不利すぎる価格はlimitPriceRangeRateの範囲で制限する
        if (orderSide.isSell()) {
          if (priceScaled.compareTo(makerBestOrder.getPrice().subtract(limitPriceRange)) < 0
              || priceScaled.compareTo(makerBestOrder.getPrice().add(limitPriceRangeMax)) > 0) {
            throw new CustomException(
                ErrorCode.ORDER_ERROR_PRICE_OUT_OF_RANGE,
                "userId: "
                    + JsonUtil.encode(user.getId())
                    + ", priceScaled: "
                    + JsonUtil.encode(priceScaled)
                    + ", makerBestOrder_Price: "
                    + JsonUtil.encode(makerBestOrder.getPrice())
                    + ", limitPriceRange: "
                    + JsonUtil.encode(limitPriceRange)
                    + ", makerBestOrder_Price_minus_limitPriceRange: "
                    + JsonUtil.encode(makerBestOrder.getPrice().subtract(limitPriceRange))
                    + ", form: "
                    + JsonUtil.encode(form));
          }
        } else {
          // 例：ETHJPY 買いのとき
          // 売り板ベストプライス（最安値）30万 ＆ limitPriceRageRate 0.5 (50%)の場合
          // 40万の買いはOK、 50万の買いはNG
          if (priceScaled.compareTo(makerBestOrder.getPrice().add(limitPriceRange)) > 0
              || priceScaled.compareTo(makerBestOrder.getPrice().subtract(limitPriceRangeMax)) < 0) {
            throw new CustomException(
                ErrorCode.ORDER_ERROR_PRICE_OUT_OF_RANGE,
                "userId: "
                    + JsonUtil.encode(user.getId())
                    + ", priceScaled: "
                    + JsonUtil.encode(priceScaled)
                    + ", makerBestOrder_Price: "
                    + JsonUtil.encode(makerBestOrder.getPrice())
                    + ", limitPriceRange: "
                    + JsonUtil.encode(limitPriceRange)
                    + ", makerBestOrder_Price_plus_limitPriceRange: "
                    + JsonUtil.encode(makerBestOrder.getPrice().add(limitPriceRange))
                    + ", form: "
                    + JsonUtil.encode(form));
          }
        }
      } else {
        // 逆指値：反対サイドのベストプライスより有利な発注価格はNG
        if (orderSide.isSell()) {
          // 売り逆指値：反対サイドのベストプライス（一番高い買い価格）以上の価格はエラー（同一価格 or 有利価格)
          if (priceScaled.compareTo(makerBestOrder.getPrice()) > -1) {
            throw new CustomException(
                ErrorCode.ORDER_ERROR_PRICE_OUT_OF_RANGE,
                "userId: "
                    + JsonUtil.encode(user.getId())
                    + ", priceScaled: "
                    + JsonUtil.encode(priceScaled)
                    + ", makerBestOrder_Price: "
                    + JsonUtil.encode(makerBestOrder.getPrice())
                    + ", form: "
                    + JsonUtil.encode(form));
          }
        } else {
          // 買い逆指値：反対サイドのベストプライス（一番安い売り価格）以下の価格はエラー（同一価格 or 有利価格)
          if (priceScaled.compareTo(makerBestOrder.getPrice()) < 1) {
            throw new CustomException(
                ErrorCode.ORDER_ERROR_PRICE_OUT_OF_RANGE,
                "userId: "
                    + JsonUtil.encode(user.getId())
                    + ", priceScaled: "
                    + JsonUtil.encode(priceScaled)
                    + ", makerBestOrder_Price: "
                    + JsonUtil.encode(makerBestOrder.getPrice())
                    + ", form: "
                    + JsonUtil.encode(form));
          }
        }
      }
    }

    // 発注総額概算算出
    // ロック資産は桁数処理を行わない（入出金対象外であり、かつ部分約定時に端数が残るのを回避するため)
    BigDecimal assetAmount = priceScaled.multiply(amountScaled);

    // 発注総額チェック:valid
    if (assetAmount == null || assetAmount.signum() < 1) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_INVALID_AMOUNT,
          "assetAmount: "
              + JsonUtil.encode(assetAmount)
              + ", userId: "
              + JsonUtil.encode(user.getId())
              + ", form: "
              + JsonUtil.encode(form));
    }

    BigDecimal assetAmountScaled =
        SpotTradeService.getBean(symbol)
            .calculateAssetAmount(symbol, priceScaled.multiply(amountScaled));

    if (assetAmountScaled == null || assetAmountScaled.signum() < 1) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_INVALID_AMOUNT,
          "assetAmount: "
              + JsonUtil.encode(assetAmount)
              + "assetAmountScaled"
              + JsonUtil.encode(assetAmountScaled)
              + ", userId: "
              + JsonUtil.encode(user.getId())
              + ", form: "
              + JsonUtil.encode(form));
    }

    // 手数料算出
    BigDecimal fee =
        SpotTradeService.getBean(symbol)
            .calculateFee(symbol, currencyPairConfig, assetAmountScaled, TradeAction.TAKER);
    if (fee == null) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_INVALID_FEE,
          "fee: " + JsonUtil.encode(fee) + ", form: " + JsonUtil.encode(form));
    }

    // 発注総額+手数料算出 ※利用可能額チェック用
    BigDecimal AssetAmountWithFee = assetAmountScaled.add(fee);

    // 平均価格算出(桁数処理後・通貨ペア単位)(逆指値のみ)
    // 逆指値の利用可能額チェック用(成行同様)
    BigDecimal orderPriceAverage = BigDecimal.ZERO;

    if (orderType == OrderType.STOP) {
      orderPriceAverage =
          orderSide.isSell()
              ? currencyPairConfig
                  .getCurrencyPair()
                  .getScaledPrice(
                      orderBookService
                          .get(symbol)
                          .getAverageBid(
                              symbol, currencyPairConfig, amountScaled, orderType, orderSide),
                      RoundingMode.FLOOR)
              : currencyPairConfig
                  .getCurrencyPair()
                  .getScaledPrice(
                      orderBookService
                          .get(symbol)
                          .getAverageAsk(
                              symbol, currencyPairConfig, amountScaled, orderType, orderSide),
                      RoundingMode.FLOOR);
    }

    // 発注総額概算算出(逆指値のみ)
    // 利用可能額チェック用の平均価格での概算算出
    // 逆指値は成行と異なりpriceにトリガー価格を保持するため、ロック資産は指値と同じとする
    BigDecimal assetAmountOrder =
        (orderType == OrderType.STOP) ? orderPriceAverage.multiply(amountScaled) : assetAmount;

    // 発注総額概算チェック:valid
    if (assetAmountOrder == null || assetAmountOrder.signum() < 1) {
      throw new CustomException(
          ErrorCode.ORDER_ERROR_INVALID_AMOUNT,
          "assetAmountOrder: "
              + JsonUtil.encode(assetAmountOrder)
              + ", userId: "
              + JsonUtil.encode(user.getId())
              + ", form: "
              + JsonUtil.encode(form));
    }

    return (E)
        redisManager.executeWithLock(
            getLockKey(user.getId(), baseCurrency),
            LockParams.ORDER,
            () -> {
              return (E)
                  redisManager.executeWithLock(
                      getLockKey(user.getId(), quoteCurrency),
                      LockParams.ORDER,
                      () -> {
                        return (E)
                            customTransactionManager.execute(
                                entityManager -> {
                                  log.info(
                                      "ordertradelog,symbolId,"
                                          + symbol.getId()
                                          + ",limitorder_transaction_start");

                                  // 【注意】トランザクション内ではentityManager引数無しのクエリを使用しないこと(save,find等。findOneはentityManager引数必須)
                                  // トランザクション内では、エラー時roll
                                  // backさせるためexceptionを出す(save,cancel処理含む)。try-catch使用注意
                                  Asset quoteAsset =
                                      assetService.findOrCreate(
                                          user.getId(), quoteCurrency, entityManager);

                                  Asset baseAsset =
                                      assetService.findOrCreate(
                                          user.getId(), baseCurrency, entityManager);

                                  if (baseAsset == null || quoteAsset == null) {
                                    throw new CustomException(
                                        ErrorCode.ORDER_ERROR_ASSET_NOT_FOUND,
                                        "userId: "
                                            + JsonUtil.encode(user.getId())
                                            + ", symbol: "
                                            + JsonUtil.encode(symbol)
                                            + ", baseAsset: "
                                            + JsonUtil.encode(baseAsset)
                                            + ", quoteAsset: "
                                            + JsonUtil.encode(quoteAsset));
                                  }

                                  // 利用可能額チェック
                                  if (orderSide.isSell()) {
                                    if (baseAsset
                                            .getOnhandAmount()
                                            .subtract(baseAsset.getLockedAmount())
                                            .compareTo(amountScaled)
                                        < 0) {
                                      throw new CustomException(
                                          ErrorCode.ORDER_ERROR_AMOUNT_EXCEED_ASSET,
                                          "onhandAmount: "
                                              + JsonUtil.encode(baseAsset.getOnhandAmount())
                                              + ", lockedAmount: "
                                              + JsonUtil.encode(baseAsset.getLockedAmount())
                                              + ", remainingAmount: "
                                              + JsonUtil.encode(
                                                  baseAsset
                                                      .getOnhandAmount()
                                                      .subtract(baseAsset.getLockedAmount()))
                                              + ", amountScaled(=lockAmountOrder): "
                                              + JsonUtil.encode(amountScaled)
                                              + ", userId: "
                                              + JsonUtil.encode(user.getId())
                                              + ", form: "
                                              + JsonUtil.encode(form));
                                    }
                                  } else {

                                    if (quoteAsset
                                            .getOnhandAmount()
                                            .subtract(quoteAsset.getLockedAmount())
                                            .compareTo(AssetAmountWithFee)
                                        < 0) {
                                      throw new CustomException(
                                          ErrorCode.ORDER_ERROR_AMOUNT_EXCEED_ASSET,
                                          "onhandAmount: "
                                              + JsonUtil.encode(quoteAsset.getOnhandAmount())
                                              + ", lockedAmount: "
                                              + JsonUtil.encode(quoteAsset.getLockedAmount())
                                              + ", remainingAmount: "
                                              + JsonUtil.encode(
                                                  quoteAsset
                                                      .getOnhandAmount()
                                                      .subtract(quoteAsset.getLockedAmount()))
                                              + ", assetAmount: "
                                              + JsonUtil.encode(assetAmount)
                                              + ", AssetAmountWithFee: "
                                              + JsonUtil.encode(AssetAmountWithFee)
                                              + ", userId: "
                                              + JsonUtil.encode(user.getId())
                                              + ", form: "
                                              + JsonUtil.encode(form));
                                    }

                                    // 逆指値：平均価格で算出した発注総額でチェック(成行同様)
                                    if (orderType == OrderType.STOP
                                        && quoteAsset
                                                .getOnhandAmount()
                                                .subtract(quoteAsset.getLockedAmount())
                                                .compareTo(assetAmountOrder)
                                            < 0) {
                                      throw new CustomException(
                                          ErrorCode.ORDER_ERROR_AMOUNT_EXCEED_ASSET,
                                          "quote_onhandAmount: "
                                              + JsonUtil.encode(quoteAsset.getOnhandAmount())
                                              + ", quote_lockedAmount: "
                                              + JsonUtil.encode(quoteAsset.getLockedAmount())
                                              + ", quote_remainingAmount: "
                                              + JsonUtil.encode(
                                                  quoteAsset
                                                      .getOnhandAmount()
                                                      .subtract(quoteAsset.getLockedAmount()))
                                              + ", assetAmount: "
                                              + JsonUtil.encode(assetAmount)
                                              + ", assetAmountOrder: "
                                              + JsonUtil.encode(assetAmountOrder)
                                              + ", userId: "
                                              + JsonUtil.encode(user.getId())
                                              + ", form: "
                                              + JsonUtil.encode(form));
                                    }
                                  }
                                  // 1日の取引上限チェック：通貨単位
                                  // 上限撤廃フラグ(apiより)が立っているときはスキップ
                                  if (!user.isTradeUncapped()) {
                                    BigDecimal baseOrderTradeAmountPerDay =
                                        getOrderTradeAmountPerDay(
                                            amountScaled,
                                            baseCurrencyConfig,
                                            baseCurrency,
                                            user,
                                            entityManager);

                                    if (baseOrderTradeAmountPerDay.compareTo(
                                            baseCurrencyConfig.getMaxOrderAmountPerDay())
                                        > 0) {

                                      throw new CustomException(
                                          ErrorCode.ORDER_ERROR_AMOUNT_OVER_MAX_PER_DAY,
                                          "userId: "
                                              + JsonUtil.encode(user.getId())
                                              + ", form: "
                                              + JsonUtil.encode(form));
                                    }

                                    // 逆指値の場合も希望価格での発注総額で算出する
                                    BigDecimal quoteOrderTradeAmountPerDay =
                                        getOrderTradeAmountPerDay(
                                            assetAmount,
                                            quoteCurrencyConfig,
                                            quoteCurrency,
                                            user,
                                            entityManager);

                                    if (quoteOrderTradeAmountPerDay.compareTo(
                                            quoteCurrencyConfig.getMaxOrderAmountPerDay())
                                        > 0) {

                                      throw new CustomException(
                                          ErrorCode.ORDER_ERROR_AMOUNT_OVER_MAX_PER_DAY,
                                          "userId: "
                                              + JsonUtil.encode(user.getId())
                                              + ", form: "
                                              + JsonUtil.encode(form));
                                    }
                                  }

                                  // アクティブ注文数量の上限チェック：通貨単位
                                  // 上限撤廃フラグ(apiより)が立っているときはスキップ
                                  if (!user.isTradeUncapped()) {
                                    checkMaxActiveOrderAmount(form, user.getId(), symbol, currencyPairConfig, entityManager);
                                  }

                                  // 資産ロック
                                  if (orderSide.isSell()) {
                                    // 売り：Base:注文数量でロック
                                    assetService.updateWithExternalLock(
                                        user.getId(),
                                        baseCurrency,
                                        BigDecimal.ZERO,
                                        amountScaled,
                                        entityManager);

                                  } else {
                                    // 買い：Quote:注文総額でロック
                                    // 発注価格×発注数量(指値・逆指値とも)
                                    assetService.updateWithExternalLock(
                                        user.getId(),
                                        quoteCurrency,
                                        BigDecimal.ZERO,
                                        assetAmount,
                                        entityManager);
                                  }

                                  // asset更新後チェック：残資産マイナスでないこと、各資産マイナスでないこと
                                  if (baseAsset
                                              .getOnhandAmount()
                                              .subtract(baseAsset.getLockedAmount())
                                              .signum()
                                          < 0
                                      || quoteAsset
                                              .getOnhandAmount()
                                              .subtract(quoteAsset.getLockedAmount())
                                              .signum()
                                          < 0
                                      || baseAsset.getOnhandAmount().signum() < 0
                                      || baseAsset.getLockedAmount().signum() < 0
                                      || quoteAsset.getOnhandAmount().signum() < 0
                                      || quoteAsset.getLockedAmount().signum() < 0) {

                                    throw new CustomException(
                                        ErrorCode.ORDER_ERROR_INVALID_ASSET,
                                        "baseOnhandAmount: "
                                            + JsonUtil.encode(baseAsset.getOnhandAmount())
                                            + ", baseLockedAmount: "
                                            + JsonUtil.encode(baseAsset.getLockedAmount())
                                            + ", quoteOnhandAmount: "
                                            + JsonUtil.encode(quoteAsset.getOnhandAmount())
                                            + ", quoteLockedAmount: "
                                            + JsonUtil.encode(quoteAsset.getLockedAmount())
                                            + ", userId: "
                                            + JsonUtil.encode(user.getId())
                                            + ", form: "
                                            + JsonUtil.encode(form));
                                  }

                                  E spotOrder = newEntity();
                                  spotOrder.setProperties(
                                      symbol.getId(), user.getId(), amountScaled);

                                  // PostOnly機能オフ(false固定)
                                  // spotOrder.setAnotherProperties(orderSide, orderType,
                                  // orderChannel, priceScaled,
                                  // amountScaled, OrderStatus.UNFILLED, OrderOperator.USER,
                                  // form.isPostOnly());
                                  spotOrder.setAnotherProperties(
                                      orderSide,
                                      orderType,
                                      orderChannel,
                                      priceScaled,
                                      amountScaled,
                                      OrderStatus.UNFILLED,
                                      OrderOperator.USER,
                                      false);

                                  return save(spotOrder, entityManager);
                                });
                      });
            });
  }

  private void checkInsiderAndRisker(SpotOrder spotOrder) {
    User user = userService.findOne(spotOrder.getUserId());
    StringBuilder stringBuilder = new StringBuilder();

    if (user.isInsider()) {
      stringBuilder.append("insider ordered. userId: " + user.getId() + ".\n");
    }

    if (user.isRisker()) {
      stringBuilder.append("risker ordered. userId: " + user.getId() + ".\n");
    }

    if (stringBuilder.length() > 0) {
      stringBuilder.append(
          "symbolId: "
              + spotOrder.getSymbolId()
              + ", orderSide: "
              + spotOrder.getOrderSide()
              + ", orderType: "
              + spotOrder.getOrderType()
              + ", price: "
              + spotOrder.getPrice().toPlainString()
              + ", amount: "
              + spotOrder.getAmount().toPlainString()
              + ".");
      log.info(stringBuilder.toString());
    }
  }

  public E order(Symbol symbol, User user, SpotOrderForm form, OrderChannel orderChannel)
      throws Exception {

    // サーキットブレーカー検知
    if (OrderType.valueOf(form.getOrderType()) == OrderType.MARKET
        || OrderType.valueOf(form.getOrderType()) == OrderType.STOP
        || OrderType.valueOf(form.getOrderType()) == OrderType.SIMPLE_MARKET) {
      if (isCircuitBreaking(symbol)) {
        throw new CustomException(ErrorCode.ORDER_ERROR_CIRCUIT_BREAKER);
      }
    }

    E spotOrder = null;

    switch (OrderType.valueOf(form.getOrderType())) {
      case MARKET:
      // case SIMPLE_MARKET:
        spotOrder = marketOrder(symbol, user, form, orderChannel);
        break;
      case LIMIT:
      case STOP:
        spotOrder = limitStopOrder(symbol, user, form, orderChannel);
        break;
      default:
        throw new CustomException(ErrorCode.REQUEST_ERROR_INVALID_ORDER_TYPE);
    }

    if (spotOrder != null) {
      checkInsiderAndRisker(spotOrder);
      sendOrderMail(spotOrder, mailNoreplyService, sesManager, symbol, user);
    }

    return spotOrder;
  }

  // CurrencyPairConfigから、サーキットブレーカー中かチェック
  // OrderTypeのチェックは呼び出し元で行う
  public boolean isCircuitBreaking(Symbol symbol) {
    Long nowLong = new Date().getTime();
    CurrencyPairConfig config =
        currencyPairConfigService.findByCondition(symbol.getTradeType(), symbol.getCurrencyPair());
    // 通貨ペア設定、サーキットブレーカー開始時間、サーキットブレーカー持続時間のいずれかnullならチェックしない
    if (config == null
        || config.getCircuitBreakUpdatedAt() == null
        || config.getCircuitBreakStopTimespan() == null) {
      return false;
    }

    return (nowLong - config.getCircuitBreakUpdatedAt().getTime())
        < config.getCircuitBreakStopTimespan() * DateUnit.MINUTE.getMillis();
  }

  @Override
  public void archive(Symbol symbol) {
    final int archiveSizeLimit = 1000;

    // id昇順ソート済み
    List<E> spotOrders = findInactive(symbol.getId(), archiveSizeLimit);

    if (CollectionUtils.isEmpty(spotOrders)) {
      return;
    }

    // 処理：最大idをarchive(削除)対象から除外する ※事前にid昇順ソート済み
    // 目的：Aurora fail over時のauto-increment値がPKの最大ID+1にリセットされる問題対応のため、
    // 削除時に最大IDを1件処理せずに残しておく
    spotOrders.remove(spotOrders.size() - 1);

    if (CollectionUtils.isEmpty(spotOrders)) {
      return;
    }

    StringJoiner stringJoiner = new StringJoiner(",");
    List<Long> ids = new ArrayList<>();

    spotOrders.forEach(
        spotOrder -> {
          stringJoiner.add(
              "("
                  + spotOrder.getId()
                  + ", "
                  + spotOrder.getSymbolId()
                  + ", "
                  + spotOrder.getUserId()
                  + ", '"
                  + spotOrder.getOrderSide().name()
                  + "'"
                  + ", '"
                  + spotOrder.getOrderType().name()
                  + "'"
                  + ", '"
                  + spotOrder.getOrderChannel().name()
                  + "'"
                  + ", "
                  + (spotOrder.getPrice() != null ? spotOrder.getPrice().toPlainString() : null)
                  + ", "
                  + spotOrder.getAveragePrice().toPlainString()
                  + ", "
                  + spotOrder.getAmount().toPlainString()
                  + ", "
                  + spotOrder.getRemainingAmount().toPlainString()
                  + ", '"
                  + spotOrder.getOrderStatus().name()
                  + "'"
                  + ", "
                  + (spotOrder.getCreatedAt() != null
                      ? "'"
                          + FormatUtil.format(
                              spotOrder.getCreatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS_S)
                          + "'"
                      : null)
                  + ", "
                  + (spotOrder.getUpdatedAt() != null
                      ? "'"
                          + FormatUtil.format(
                              spotOrder.getUpdatedAt(), FormatPattern.YYYY_MM_DD_HH_MM_SS_S)
                          + "'"
                      : null)
                  + ", '"
                  + spotOrder.getOrderOperator().name()
                  + "'"
                  + ", '"
                  + String.valueOf(spotOrder.isPostOnly())
                  + "'"
                  + ", "
                  + (spotOrder.getCancelReason() != null
                    ? "'"
                      + spotOrder.getCancelReason().name()
                    + "'"
                  : null)
                  + ")");
          ids.add(spotOrder.getId());
        });

    // 将spotOrders中的数据插入到历史交易表中
    historicalTransactionManager.archive(
        "insert into "
            + Order.getTableName(symbol)
            + " (id, symbol_id, user_id, order_side, order_type, order_channel, price, average_price, amount, remaining_amount, order_status, created_at, updated_at, order_operator, post_only, cancel_reason) values "
            + stringJoiner.toString());

    // 创建一个MapSqlParameterSource对象，用于存储要删除的id
    MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource();
    mapSqlParameterSource.addValue("ids", ids);
    // 将spotOrders中的数据从当前表中删除
    customTransactionManager.multiUpdate(
        "delete from " + Order.getTableName(symbol) + " where id in (:ids)", mapSqlParameterSource);

    // 记录日志
    log.info("archive_log_spot_order," + ids);
  }

  private BigDecimal getOrderTradeAmountPerDay(
      BigDecimal amount,
      CurrencyConfig currencyConfig,
      Currency curerncy,
      User user,
      EntityManager entityManager) {

    if (curerncy == Currency.JPY) {
      return BigDecimal.ZERO;
    }

    // 処理概要:
    // ・発注数量(発注総額) + 当日の未約定指値の残数量 + 当日取引履歴の数量合算が1日の取引上限を超えているか判定する
    // 備考：
    // ・指値に価格変更はないのでcreated_atでも問題ない
    // ・入力amount = baseのときはamount、quoteのときはassetAmount ※桁数処理後
    // 処理内容:
    // ・JPYはチェック対象外
    // ・入力のBASE, QUOTEともに、BASE/QUOTEそれぞれ参照し、同一通貨の場合合算する
    // ・買いも売りも正で合算する
    // ・成行・指値ともカウントする
    // ・JPYはチェック対象外
    // 例：
    // ・BTC/JPY では 買い注文と売り注文でBTCの数量をカウントする。JPYは不要。(例: 1BTC買いで BTCは+1 )
    // ・ETH/BTCは 買い注文と売り注文で通貨(BTCとETH)毎に数量をカウントする。(例: 1ETH(0.04BTC)の買いで、ETHは+1、BTCは+0.04 )
    // ・ETH/BTCの場合、BASE:ETH/BTCとETH/JPYのETHと、QUOTE:ETH/BTCとBTC/JPYのBTCそれぞれの合算で上限チェックを行う

    // 日本時間の00:00以上、翌日00:00未満をUTCのミリ秒に変換(TABLE上はUTCタイムスタンプ保持)
    Date date = new Date();
    long dateFromTmp =
        date.toInstant()
            .atZone(ZoneId.of("Asia/Tokyo"))
            .truncatedTo(ChronoUnit.DAYS)
            .withZoneSameInstant(ZoneId.of("UTC"))
            .toInstant()
            .toEpochMilli();
    long dateToTmp =
        date.toInstant()
            .atZone(ZoneId.of("Asia/Tokyo"))
            .plus(1, ChronoUnit.DAYS)
            .truncatedTo(ChronoUnit.DAYS)
            .withZoneSameInstant(ZoneId.of("UTC"))
            .toInstant()
            .toEpochMilli();

    // marketMaker用はログ抑制(appのみに実装)
    Date dateFrom = new Date(dateFromTmp);
    Date dateTo = new Date(dateToTmp);

    BigDecimal amountSumDay = BigDecimal.ZERO;
    BigDecimal amountSpotTrade = BigDecimal.ZERO;
    BigDecimal amountSpotOrder = BigDecimal.ZERO;

    List<Symbol> symbols = symbolService.findAllByCondition(TradeType.SPOT, entityManager);

    if (symbols != null) {
      for (Symbol symbolData : symbols) {

        // 当日取引履歴の同一通貨分サマリ
        List<SpotTrade> spotTrades =
            SpotTradeService.getBean(symbolData)
                .findByCondition(symbolData.getId(), user.getId(), dateFrom, dateTo, entityManager);

        if (spotTrades != null) {
          for (SpotTrade spotTrade : spotTrades) {
            if (curerncy == symbolData.getCurrencyPair().getBaseCurrency()) {

              amountSpotTrade = amountSpotTrade.add(spotTrade.getAmount());
            }

            if (curerncy == symbolData.getCurrencyPair().getQuoteCurrency()) {

              amountSpotTrade =
                  amountSpotTrade.add(spotTrade.getAmount().multiply(spotTrade.getPrice()));
            }
          }
        }

        // 当日未約定指値の同一通貨分サマリ
        List<OrderStatus> orderStatuses = new ArrayList<OrderStatus>();
        orderStatuses.add(OrderStatus.UNFILLED);
        orderStatuses.add(OrderStatus.PARTIALLY_FILLED);

        List<SpotOrder> spotOrders =
            SpotOrderService.getBean(symbolData)
                .findByCondition(
                    symbolData.getId(),
                    user.getId(),
                    null,
                    dateFrom,
                    dateTo,
                    orderStatuses,
                    entityManager);

        if (spotOrders != null) {
          for (SpotOrder spotOrder : spotOrders) {
            if (curerncy == symbolData.getCurrencyPair().getBaseCurrency()) {

              amountSpotOrder = amountSpotOrder.add(spotOrder.getRemainingAmount());
            }

            if (curerncy == symbolData.getCurrencyPair().getQuoteCurrency()) {

              amountSpotOrder =
                  amountSpotOrder.add(
                      spotOrder.getRemainingAmount().multiply(spotOrder.getPrice()));
            }
          }
        }
      }
    }

    BigDecimal amountPosTrade = BigDecimal.ZERO;
    BigDecimal amountPosOrder = BigDecimal.ZERO;
    List<Symbol> PosSymbols = symbolService.findAllByCondition(TradeType.POS, entityManager);
    if (symbols != null) {
      for (Symbol symbolData : PosSymbols) {
        // 当日取引履歴の同一通貨分サマリ
        List<PosTrade> posTrades = posTradeService.findByCondition(symbolData.getId(), user.getId(),
            dateFrom, dateTo, entityManager);
        if (posTrades != null) {
          for (PosTrade posTrade : posTrades) {
            if (curerncy == symbolData.getCurrencyPair().getBaseCurrency()) {
              amountPosTrade = amountPosTrade.add(posTrade.getAmount());
            }
            if (curerncy == symbolData.getCurrencyPair().getQuoteCurrency()) {
              amountPosTrade =
                  amountPosTrade.add(posTrade.getAmount().multiply(posTrade.getPrice()));
            }
          }
        }
        // 当日未約定指値の同一通貨分サマリ
        List<PosOrderStatus> orderStatuses = new ArrayList<>();
        orderStatuses.add(PosOrderStatus.WAITING);
        orderStatuses.add(PosOrderStatus.PARTIALLY_FILLED);
        List<PosOrder> posOrders =
            posOrderService.findByCondition(
                symbolData.getId(),
                user.getId(),
                null,
                dateFrom,
                dateTo,
                orderStatuses,
                entityManager);
        if (posOrders != null) {
          for (PosOrder posOrder : posOrders) {
            if (curerncy == symbolData.getCurrencyPair().getBaseCurrency()) {
              amountPosOrder = amountPosOrder.add(posOrder.getRemainingAmount());
            }
            if (curerncy == symbolData.getCurrencyPair().getQuoteCurrency()) {
              amountPosOrder = amountPosOrder.add(
                  posOrder.getRemainingAmount().multiply(posOrder.getPrice()));
            }
          }
        }
      }
    }

    // 合算(発注数量(総額) + 当日取引履歴の約定数量(総額) + 当日指値の発注数量(総額))
    return amountSumDay.add(amount).add(amountSpotTrade).add(amountSpotOrder).add(amountPosTrade).add(amountPosOrder);
  }

  // 使用する場合は、通貨・通貨ペアの有効チェック(configのenabled)と、assetがnullの考慮追加修正が必要
  public void checkAssetLockedAmount(
      String prefixPlace,
      String prefixBeforeAfter,
      Long userId,
      Currency baseCurrency,
      Currency quoteCurrency,
      EntityManager entityManager)
      throws Exception {

    List<Symbol> symbols = symbolService.findAllByCondition(TradeType.SPOT, entityManager);

    BigDecimal baseExpectedLockedAmount = BigDecimal.ZERO;
    BigDecimal quoteExpectedLockedAmount = BigDecimal.ZERO;

    // 引数userIdのassetを取得
    BigDecimal baseLockedAmount =
        assetService.findOrCreate(userId, baseCurrency, entityManager).getLockedAmount();
    BigDecimal quoteLockedAmount =
        assetService.findOrCreate(userId, quoteCurrency, entityManager).getLockedAmount();

    // 引数userIdの全symobolの全未約定注文を取得
    if (symbols != null) {
      for (Symbol symbolData : symbols) {

        // 未約定指値の同一通貨分サマリ
        List<OrderStatus> orderStatuses = new ArrayList<OrderStatus>();
        orderStatuses.add(OrderStatus.UNFILLED);
        orderStatuses.add(OrderStatus.PARTIALLY_FILLED);

        List<SpotOrder> spotOrders =
            SpotOrderService.getBean(symbolData)
                .findByCondition(
                    symbolData.getId(), userId, null, null, null, orderStatuses, entityManager);

        if (spotOrders != null) {
          for (SpotOrder spotOrder : spotOrders) {

            // baseの場合は残注文の売りの未約定注文の数量を合算（通貨別）
            // quoteの場合は残注文の買いの未約定注文の数量*価格を合算（通貨別）

            // 上記を通貨別に合算し、通貨別に持つべきlockedAmountを算出
            Currency spotBaseCurrency = symbolData.getCurrencyPair().getBaseCurrency();
            Currency spotQuoteCurrency = symbolData.getCurrencyPair().getQuoteCurrency();

            if (spotOrder.getOrderSide() == OrderSide.SELL) {
              if (spotBaseCurrency == baseCurrency) {
                baseExpectedLockedAmount =
                    baseExpectedLockedAmount.add(spotOrder.getRemainingAmount());
              }

              if (spotBaseCurrency == quoteCurrency) {
                quoteExpectedLockedAmount =
                    quoteExpectedLockedAmount.add(spotOrder.getRemainingAmount());
              }
            }

            if (spotOrder.getOrderSide() == OrderSide.BUY) {
              if (spotQuoteCurrency == baseCurrency) {

                baseExpectedLockedAmount =
                    baseExpectedLockedAmount.add(
                        spotOrder.getPrice().multiply(spotOrder.getRemainingAmount()));
              }
              if (spotQuoteCurrency == quoteCurrency) {

                quoteExpectedLockedAmount =
                    quoteExpectedLockedAmount.add(
                        spotOrder.getPrice().multiply(spotOrder.getRemainingAmount()));
              }
            }
          }
        }
      }
    }

    // assetのlockedAmountと上記合算値(持つべきlockedAmount)を通貨別に比較
    // 相違が出た場合、prefix付きのlogを出力する
    // log項目：prefix, userId, 通貨, lockdAmount, 持つべきlockedAmount

    if (baseLockedAmount.compareTo(baseExpectedLockedAmount) != 0) {
      // prefixPlace:order / trade / asset / cancel / rebalance
      // prefixBeforeAfter: before / after
      log.info(
          "InvalidLockedAmountLog,"
              + prefixPlace
              + ","
              + prefixBeforeAfter
              + ","
              + userId
              + ","
              + baseCurrency
              + ","
              + baseLockedAmount
              + ","
              + baseExpectedLockedAmount);
    }
    if (quoteLockedAmount.compareTo(quoteExpectedLockedAmount) != 0) {
      log.info(
          "InvalidLockedAmountLog,"
              + prefixPlace
              + ","
              + prefixBeforeAfter
              + ","
              + userId
              + ","
              + quoteCurrency
              + ","
              + quoteLockedAmount
              + ","
              + quoteExpectedLockedAmount);
    }

    return;
  }

  boolean checkMaxActiveOrderAmount(SpotOrderForm form, Long userId, Symbol symbol,
      CurrencyPairConfig currencyPairConfig, EntityManager entityManager) throws CustomException {
    // 板注文数量上限チェック：通貨ペア単位
    var builder = entityManager.getCriteriaBuilder();
    var cquery = builder.createQuery(BigDecimal.class);
    var root = cquery.from(getEntityClass());
    var activeOrderAmount = entityManager
        .createQuery(cquery.select(builder.sum(root.get(SpotOrder_.remainingAmount)))
            .where(
              builder.and(
                root.get(SpotOrder_.orderStatus).in((Object[]) OrderStatus.ACTIVE_ORDER_STATUSES),
                builder.equal(root.get(SpotOrder_.userId), userId),
                builder.equal(root.get(SpotOrder_.symbolId), symbol.getId()))
        ))
        .getSingleResult();
    if (activeOrderAmount == null) {
      activeOrderAmount = BigDecimal.ZERO;
    }

    if (activeOrderAmount.add(form.getAmount()).compareTo(currencyPairConfig.getMaxActiveOrderAmount()) > 0) {
      throw new CustomException(ErrorCode.ORDER_ERROR_ACTIVE_AMOUNT_OVER_MAX,
          "orderform: " + JsonUtil.encode(form));
    }
    return true;
  }

  void sendOrderMail(SpotOrder spotOrder, MailNoreplyService mailNoreplyService, SesManager sesManager, Symbol symbol, User user) throws Exception {
    if (user == null || user.isTradeUncapped()) {
      return;
    }
    try {
      // get mail template
      var mailInfo = mailNoreplyService.findOne(MailNoreplyType.ORDER);
      var mailTemplate = mailInfo.getContents();
      mailTemplate = mailTemplate.replace("${instrumentId}", symbol.getCurrencyPair().getName());
      mailTemplate = mailTemplate.replace("${side}", spotOrder.getOrderSide().toString());
      mailTemplate = mailTemplate.replace("${orderType}", spotOrder.getOrderType().toString());
      mailTemplate = mailTemplate.replace("${size}", FormatUtil.formatThousandsSeparator(symbol.getCurrencyPair().getScaledAmount(spotOrder.getAmount()).toPlainString()));
      mailTemplate = mailTemplate.replace("${price}", FormatUtil.formatThousandsSeparator(symbol.getCurrencyPair().getScaledPrice(spotOrder.getPrice()).toPlainString()));
      mailTemplate = mailTemplate.replace("${orderId}", spotOrder.getId().toString());
      // send mail
      sesManager.send(mailInfo.getFromAddress(), user.getEmail(), mailInfo.getTitle(), mailTemplate);
    } catch (Exception e) {
      log.error("sendOrderMail error", e);
    }
  }


  void sendCustomerCancelMail(SpotOrder spotOrder, MailNoreplyService mailNoreplyService, SesManager sesManager, Symbol symbol, User user) throws Exception {
    if (user == null || user.isTradeUncapped()) {
      return;
    }
    try {
      // get mail template
      var mailInfo = mailNoreplyService.findOne(MailNoreplyType.DELETE_ORDER);
      var mailTemplate = mailInfo.getContents();
      mailTemplate = mailTemplate.replace("${instrumentId}", symbol.getCurrencyPair().getName());
      mailTemplate = mailTemplate.replace("${side}", spotOrder.getOrderSide().toString());
      mailTemplate = mailTemplate.replace("${orderType}", spotOrder.getOrderType().toString());
      mailTemplate = mailTemplate.replace("${size}", FormatUtil.formatThousandsSeparator(symbol.getCurrencyPair().getScaledAmount(spotOrder.getAmount()).toPlainString()));
      mailTemplate = mailTemplate.replace("${price}", FormatUtil.formatThousandsSeparator(symbol.getCurrencyPair().getScaledPrice(spotOrder.getPrice()).toPlainString()));
      mailTemplate = mailTemplate.replace("${orderId}", spotOrder.getId().toString());
      mailTemplate = mailTemplate.replace("${remaining_amount}", FormatUtil.formatThousandsSeparator(symbol.getCurrencyPair().getScaledAmount(spotOrder.getRemainingAmount()).toPlainString()));
      // send mail
      sesManager.send(mailInfo.getFromAddress(), user.getEmail(), mailInfo.getTitle(), mailTemplate);
    } catch (Exception e) {
      log.error("sendCustomerCancelMail error", e);
    }
  }

  void sendOtherCancelMail(SpotOrder spotOrder, MailNoreplyService mailNoreplyService, SesManager sesManager, Symbol symbol, User user) throws Exception {
    if (user == null || user.isTradeUncapped()) {
      return;
    }
    try {
      // get mail template
      var mailInfo = mailNoreplyService.findOne(MailNoreplyType.EXPIRED_ORDER);
      var mailTemplate = mailInfo.getContents();
      mailTemplate = mailTemplate.replace("${instrumentId}", symbol.getCurrencyPair().getName());
      mailTemplate = mailTemplate.replace("${side}", spotOrder.getOrderSide().toString());
      mailTemplate = mailTemplate.replace("${orderType}", spotOrder.getOrderType().toString());
      mailTemplate = mailTemplate.replace("${size}", FormatUtil.formatThousandsSeparator(symbol.getCurrencyPair().getScaledAmount(spotOrder.getAmount()).toPlainString()));
      mailTemplate = mailTemplate.replace("${price}", FormatUtil.formatThousandsSeparator(symbol.getCurrencyPair().getScaledPrice(spotOrder.getPrice()).toPlainString()));
      mailTemplate = mailTemplate.replace("${orderId}", spotOrder.getId().toString());
      mailTemplate = mailTemplate.replace("${remaining_amount}", FormatUtil.formatThousandsSeparator(symbol.getCurrencyPair().getScaledAmount(spotOrder.getRemainingAmount()).toPlainString()));
      // send mail
      sesManager.send(mailInfo.getFromAddress(), user.getEmail(), mailInfo.getTitle(), mailTemplate);
    } catch (Exception e) {
      log.error("sendOtherCancelMail error", e);
    }
  }

  @Override
  public void redisPublish(E entity){
    redisPublisher.publish(entity);
  }
}
