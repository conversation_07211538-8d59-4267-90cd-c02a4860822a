package exchange.spot.service;

import org.springframework.stereotype.Service;
import exchange.spot.entity.SpotCoverOrderAdaJpy;
import exchange.spot.predicate.SpotCoverOrderAdaJpyPredicate;

@Service
public class SpotCoverOrderAdaJpyService
    extends SpotCoverOrderService<SpotCoverOrderAdaJpy, SpotCoverOrderAdaJpyPredicate> {

  @Override
  public Class<SpotCoverOrderAdaJpy> getEntityClass() {
    return SpotCoverOrderAdaJpy.class;
  }
}
