SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
create table if not exists user_status_history
(
    id            bigint auto_increment
    primary key,
    user_id   bigint       null,
    user_info_id bigint     null,
    before_status varchar(200) null,
    after_status  varchar(200) null,
    reason        varchar(200) null,
    staff_id      bigint       null,
    created_at    bigint       null,
    updated_at    bigint       null,
    constraint user_status_history_user_id_fk
    foreign key (user_id) references user (id),
    constraint user_status_history_user_info_id_fk
    foreign key (user_info_id) references user_info (id)
    )ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;